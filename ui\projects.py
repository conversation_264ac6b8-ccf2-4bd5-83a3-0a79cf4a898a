import datetime
import platform
import ctypes
from ctypes import wintypes
import os
import csv
import json
import traceback
import subprocess
from datetime import datetime as dt
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QTabWidget, QSplitter, QFrame, QFileDialog, QProgressBar,
                            QMenu, QAction, QSizePolicy, QListWidget, QListWidgetItem,
                            QTextBrowser, QScrollArea, QGridLayout)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import (QIcon, QFont, QColor, QPainter, QPixmap, QBrush, QPen,
                        QRadialGradient, QLinearGradient, QTextDocument)
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import (Project, Client, Document, Property, PropertyDocument, get_session)
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency, safe_edit_item,
                    safe_get_id_from_table)
from ui.properties import PropertiesWidget
from ui.unified_styles import UnifiedStyles, StyledButton, StyledTable, StyledTabWidget, StyledGroupBox
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin
from sqlalchemy import func
import ctypes
from ctypes import wintypes


class ProjectWarningDialog(QDialog):
    """نافذة تحذير متطورة للمشاريع مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تحذير", message="", icon="⚠️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المشاريع المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(251, 191, 36, 0.2),
                    stop:0.5 rgba(245, 158, 11, 0.3),
                    stop:1 rgba(217, 119, 6, 0.2));
                border: 2px solid rgba(251, 191, 36, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التحذير
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ تم فهم التحذير؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class ProjectConfirmationDialog(QDialog):
    """نافذة تأكيد متطورة للمشاريع مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تأكيد", message="", icon="❓"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المشاريع المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التأكيد
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # سؤال التأكيد
        question_label = QLabel("❓ هل تريد المتابعة؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("✅ تأكيد")
        self.style_advanced_button(confirm_button, 'success')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class ProjectSuccessDialog(QDialog):
    """نافذة نجاح متطورة للمشاريع مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="نجح", message="", icon="✅"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المشاريع المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.2),
                    stop:0.5 rgba(5, 150, 105, 0.3),
                    stop:1 rgba(4, 120, 87, 0.2));
                border: 2px solid rgba(16, 185, 129, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة النجاح
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # رسالة التأكيد
        question_label = QLabel("✅ تمت العملية بنجاح!")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #10b981;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class ProjectErrorDialog(QDialog):
    """نافذة خطأ متطورة للمشاريع مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="خطأ", message="", icon="❌"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المشاريع المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة الخطأ
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # رسالة التأكيد
        question_label = QLabel("❌ حدث خطأ!")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #ef4444;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("❌ إغلاق")
        self.style_advanced_button(ok_button, 'danger')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class DeleteProjectDialog(QDialog):
    """نافذة حذف المشروع مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, project=None):
        super().__init__(parent)
        self.project = project
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("🏗️ حذف - نظام إدارة المشاريع المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("🏗️ حذف المشروع")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات المشروع مضغوطة
        if self.project:
            info_text = f"🏗️ {self.project.name[:15]}{'...' if len(self.project.name) > 15 else ''}"
            if self.project.budget:
                info_text += f" | 💰 {self.project.budget:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("🏗️ حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🏗️")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان - موحد مع باقي النوافذ"""
        try:
            from ui.suppliers import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            try:
                from ui.title_bar_styler import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(self)
            except Exception as e2:
                print(f"خطأ في تطبيق تصميم شريط العنوان: {e2}")

class ProjectDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل مشروع - مطابقة للتصميم الموحد"""

    def __init__(self, parent=None, project=None, session=None):
        super().__init__(parent)
        self.project = project
        self.session = session
        self.parent_widget = parent
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار مطابق للتصميم الموحد
        if self.project:
            self.setWindowTitle("🏗️ تعديل مشروع - نظام إدارة المشاريع المتطور والشامل")
        else:
            self.setWindowTitle("🏗️ إضافة مشروع جديد - نظام إدارة المشاريع المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        self.setModal(True)
        self.resize(650, 650)

        # إنشاء التخطيط الرئيسي مطابق للتصميم الموحد
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # عنوان النافذة مطابق للعملاء والموردين
        title_text = "تعديل بيانات المشروع" if self.project else "إضافة مشروع جديد"
        title_label = QLabel(f"🏗️ {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
                font-weight: bold;
                font-size: 20px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء نموذج معلومات المشروع مطابق للتصميم الموحد
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل - مطابقة للعملاء والموردين
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # حقل اسم المشروع مطابق للعملاء والموردين
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المشروع...")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        if self.project:
            self.name_edit.setText(self.project.name)
        form_layout.addRow(create_styled_label("اسم المشروع", "🏗️", True), self.name_edit)

        # حقل العميل مطابق للعملاء والموردين
        self.client_combo = QComboBox()
        self.client_combo.addItem("-- اختر عميل --", None)
        self.client_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)

        # إضافة العملاء من قاعدة البيانات
        if self.session:
            clients = self.session.query(Client).all()
            for client in clients:
                self.client_combo.addItem(client.name, client.id)

        # تحديد العميل الحالي إذا كان موجودًا
        if self.project and self.project.client_id:
            index = self.client_combo.findData(self.project.client_id)
            if index >= 0:
                self.client_combo.setCurrentIndex(index)

        form_layout.addRow(create_styled_label("العميل", "👤"), self.client_combo)

        # حقل الموقع مطابق للعملاء والموردين
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("أدخل موقع المشروع...")
        self.location_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        if self.project:
            self.location_edit.setText(self.project.location or "")
        form_layout.addRow(create_styled_label("الموقع", "📍"), self.location_edit)

        # حقل المساحة مطابق للعملاء والموردين
        self.area_edit = QDoubleSpinBox()
        self.area_edit.setRange(0, 10000)
        self.area_edit.setDecimals(0)
        self.area_edit.setSuffix(" م²")
        self.area_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        if self.project:
            self.area_edit.setValue(self.project.area or 0)
        form_layout.addRow(create_styled_label("المساحة", "📐"), self.area_edit)

        # حقل تاريخ البدء مطابق للعملاء والموردين
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        self.start_date_edit.setStyleSheet("""
            QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        if self.project and self.project.start_date:
            self.start_date_edit.setDate(datetime_to_qdate(self.project.start_date))
        form_layout.addRow(create_styled_label("تاريخ البدء", "📅"), self.start_date_edit)

        # حقل تاريخ الانتهاء المتوقع مطابق للعملاء والموردين
        self.expected_end_date_edit = QDateEdit()
        self.expected_end_date_edit.setCalendarPopup(True)
        self.expected_end_date_edit.setDate(QDate.currentDate().addMonths(1))
        self.expected_end_date_edit.setStyleSheet("""
            QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        if self.project and self.project.expected_end_date:
            self.expected_end_date_edit.setDate(datetime_to_qdate(self.project.expected_end_date))
        form_layout.addRow(create_styled_label("تاريخ الانتهاء المتوقع", "⏰"), self.expected_end_date_edit)

        # حقل الحالة مطابق للمخزن مع خيارات متعددة
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(245, 158, 11, 0.3);
                box-shadow: 0 4px 15px rgba(245, 158, 11, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
                transform: scale(1.02);
            }
        """)
        # خيارات متعددة لحالات المشاريع مطابقة للمخزن
        statuses = ["planning", "in_progress", "completed", "cancelled", "on_hold", "review", "approved"]
        status_labels = ["قيد التخطيط", "قيد التنفيذ", "مكتمل", "ملغى", "معلق", "قيد المراجعة", "معتمد"]

        for i, status in enumerate(statuses):
            self.status_combo.addItem(status_labels[i], status)

        if self.project and self.project.status:
            index = self.status_combo.findData(self.project.status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

        form_layout.addRow(create_styled_label("الحالة", "📊"), self.status_combo)

        # حقل الميزانية مطابق للعملاء والموردين
        self.budget_edit = QDoubleSpinBox()
        self.budget_edit.setRange(0, 10000000)
        self.budget_edit.setDecimals(0)
        self.budget_edit.setSingleStep(1000)
        self.budget_edit.setSuffix(" جنيه")
        self.budget_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        if self.project:
            self.budget_edit.setValue(self.project.budget or 0)
        form_layout.addRow(create_styled_label("الميزانية", "💰"), self.budget_edit)

        # حقل الملاحظات مطابق للعملاء والموردين
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل ملاحظات حول المشروع...")
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 60px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        if self.project and self.project.notes:
            self.notes_edit.setText(self.project.notes)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        # إضافة النموذج للتخطيط الرئيسي
        main_layout.addLayout(form_layout)

        # أزرار التحكم - مطابقة للعملاء والموردين (ترتيب صحيح)
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.save_project)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # ترتيب صحيح: الحفظ أولاً ثم الإلغاء
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)



    def save_project(self):
        """حفظ بيانات المشروع"""
        try:
            # التحقق من صحة البيانات
            name = self.name_edit.text().strip()
            if not name:
                dialog = ProjectWarningDialog(self, "خطأ", "يجب إدخال اسم المشروع", "⚠️")
                dialog.exec_()
                return

            client_id = self.client_combo.currentData()
            if not client_id:
                dialog = ProjectWarningDialog(self, "خطأ", "يجب اختيار عميل", "⚠️")
                dialog.exec_()
                return

            # جمع البيانات
            location = self.location_edit.text().strip()
            area = self.area_edit.value()
            from utils import qdate_to_datetime
            start_date = qdate_to_datetime(self.start_date_edit.date())
            expected_end_date = qdate_to_datetime(self.expected_end_date_edit.date())
            status = self.status_combo.currentData()
            budget = self.budget_edit.value()
            notes = self.notes_edit.toPlainText().strip()

            # إنشاء أو تحديث المشروع
            if self.project:
                # تحديث مشروع موجود
                self.project.name = name
                self.project.client_id = client_id
                self.project.location = location
                self.project.area = area
                self.project.start_date = start_date
                self.project.expected_end_date = expected_end_date
                self.project.status = status
                self.project.budget = budget
                self.project.notes = notes
            else:
                # إنشاء مشروع جديد
                from database import Project
                self.project = Project(
                    name=name,
                    client_id=client_id,
                    location=location,
                    area=area,
                    start_date=start_date,
                    expected_end_date=expected_end_date,
                    status=status,
                    budget=budget,
                    notes=notes
                )
                self.session.add(self.project)

            # حفظ في قاعدة البيانات
            self.session.commit()
            self.accept()

        except Exception as e:
            dialog = ProjectErrorDialog(self, "خطأ", f"حدث خطأ في حفظ المشروع: {str(e)}", "❌")
            dialog.exec_()

    def get_data(self):
        """الحصول على بيانات المشروع من النموذج - للتوافق مع الكود القديم"""
        name = self.name_edit.text().strip()
        client_id = self.client_combo.currentData()
        location = self.location_edit.text().strip()
        area = self.area_edit.value()
        from utils import qdate_to_datetime
        start_date = qdate_to_datetime(self.start_date_edit.date())
        expected_end_date = qdate_to_datetime(self.expected_end_date_edit.date())
        status = self.status_combo.currentData()
        budget = self.budget_edit.value()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not name:
            dialog = ProjectWarningDialog(self, "خطأ", "يجب إدخال اسم المشروع", "⚠️")
            dialog.exec_()
            return None

        if not client_id:
            dialog = ProjectWarningDialog(self, "خطأ", "يجب اختيار عميل", "⚠️")
            dialog.exec_()
            return None

        if expected_end_date <= start_date:
            dialog = ProjectWarningDialog(self, "خطأ", "يجب أن يكون تاريخ الانتهاء المتوقع بعد تاريخ البدء", "⚠️")
            dialog.exec_()
            return None

        return {
            'name': name,
            'client_id': client_id,
            'location': location,
            'area': area,
            'start_date': start_date,
            'expected_end_date': expected_end_date,
            'actual_end_date': actual_end_date,
            'status': status,
            'budget': budget,
            'description': description,
            'notes': notes
        }

    def customize_title_bar(self):
        """تخصيص شريط العنوان - مطابق للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للمشاريع مطابقة للعملاء والموردين
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            gradient = QLinearGradient(0, 0, 32, 32)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.drawRoundedRect(2, 2, 28, 28, 6, 6)

            # إضافة رمز المشروع
            painter.setPen(QColor(255, 255, 255))
            font = painter.font()
            font.setPointSize(16)
            font.setBold(True)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "🏗️")

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان - موحد مع باقي النوافذ"""
        try:
            from ui.suppliers import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            try:
                from ui.title_bar_styler import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(self)
            except Exception as e2:
                print(f"خطأ في تطبيق تصميم شريط العنوان: {e2}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - نسخة مبسطة لنافذة الحوار مطابقة للعملاء والموردين"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل مطابق للعملاء والموردين
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

class ProjectsWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة المشاريع مع التحديد المتعدد"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        self.init_ui()
        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(250, self.refresh_data)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعمال مع تقليل المساحات الفارغة
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إنشاء تبويبات للإنشاء والإستثمار والبيانات العامة للعقارات مع تنسيق مطابق للعمال
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.3 rgba(241, 245, 249, 0.9),
                    stop:0.7 rgba(226, 232, 240, 0.85),
                    stop:1 rgba(255, 255, 255, 0.9));
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 880px;
                max-width: 880px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 880px;
                max-width: 880px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 880px;
                max-width: 880px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)
        # تبويب الإنشاء والإستثمار مع تقليل المساحات الفارغة
        projects_tab = QWidget()
        projects_layout = QVBoxLayout()
        projects_layout.setContentsMargins(1, 1, 1, 1)  # تقليل الهوامش من 5 إلى 1
        projects_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🏗️ إدارة المشاريع المتطورة - نظام شامل ومتقدم لإدارة المشاريع مع أدوات احترافية للبحث والتحليل")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        projects_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الموقع، العميل أو الوصف...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        search_button.clicked.connect(self.filter_projects)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية بسيطة للحالات
        self.status_filter_frame = QLabel("جميع الحالات")
        self.status_filter_frame.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.5 rgba(248, 250, 252, 0.95),
                    stop:1 rgba(241, 245, 249, 0.9));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 10px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
                color: #1e40af;
                min-width: 120px;
                text-align: center;
            }
        """)
        self.current_filter_value = None

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المشاريع المتطور والمحسن
        self.create_advanced_projects_table()

        projects_layout.addWidget(top_frame)
        projects_layout.addWidget(self.projects_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مشروع")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_project)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_project)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_project)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')  # سيان مطابق للعملاء والموردين والعمال
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.view_button.clicked.connect(self.view_project)

        self.documents_button = QPushButton("📁 إدارة الوثائق")
        self.style_advanced_button(self.documents_button, 'gray')  # رمادي مطابق للعملاء والموردين والعمال
        self.documents_button.clicked.connect(self.manage_documents)
        self.documents_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)



        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)  # أسود مطابق للعملاء والموردين والعمال
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير مطابقة تماماً لقسم العملاء
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 5px 10px 5px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: left;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                padding-left: 110px !important;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(self.export_to_excel_advanced)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(self.export_to_csv_advanced)
        export_menu.addAction(csv_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        detailed_action = QAction("📊 تقرير تفصيلي", self)
        detailed_action.triggered.connect(self.export_detailed_report)
        export_menu.addAction(detailed_action)

        balance_action = QAction("💰 تقرير الأرصدة", self)
        balance_action.triggered.connect(self.export_balance_report)
        export_menu.addAction(balance_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(self.export_custom)
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(self.export_backup)
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(self.restore_backup)
        export_menu.addAction(restore_action)

        # تخصيص موضع وعرض القائمة
        def show_export_menu():
            """عرض قائمة التصدير فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.export_button.mapToGlobal(self.export_button.rect().topLeft())

            # تحديد عرض القائمة مع تكبير ربع درجة
            button_width = self.export_button.width()
            export_menu.setFixedWidth(max(button_width, 190))

            # ترحيل القائمة نصف درجة لليسار
            button_pos.setX(button_pos.x() - 10)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = export_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            export_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة بدلاً من setMenu
        self.export_button.clicked.connect(show_export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()

        # إجمالي المشاريع مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي المشاريع: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.documents_button)

        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.columns_visibility_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع تخطيط تبويب المشاريع
        projects_layout.addWidget(bottom_frame)
        projects_tab.setLayout(projects_layout)

        # تبويب البيانات العامة للعقارات
        self.properties_widget = PropertiesWidget(self.session)

        # إضافة التبويبات مع أيقونات مثل العمال
        self.tabs.addTab(projects_tab, "🏗️ بيانات المشاريع")
        self.tabs.addTab(self.properties_widget, "🏢 إدارة العقارات")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        # تهيئة حالة الأزرار (جميع الأزرار مفعلة ومنيرة في البداية)
        QTimer.singleShot(100, self.initialize_button_states)

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث
            if hasattr(self, 'search_edit'):
                self.search_edit.textChanged.connect(self.filter_projects)

            # ربط خاصية النقر المزدوج للتعديل
            self.projects_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

            # ربط حدث تغيير التحديد - مطابق للعملاء
            self.projects_table.itemSelectionChanged.connect(self.on_projects_selection_changed)

            print("✅ تم ربط أحداث المشاريع بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث المشاريع: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية مطابق للعملاء"""
        try:
            self.edit_project()
        except Exception as e:
            pass  # خطأ في النقر المزدوج

    def on_projects_selection_changed(self):
        """معالج تغيير التحديد مطابق للعملاء"""
        try:
            print("🚨 تم استدعاء معالج تحديد المشاريع!")

            # تسجيل أن المستخدم تفاعل مع الجدول
            self.user_interacted_with_table = True
            print("👆 المستخدم تفاعل مع الجدول - سيتم تطبيق خاصية الإغلاق")

            self.update_selected_projects_list()
            self.update_button_states()

        except Exception as e:
            print(f"❌ خطأ في معالجة تحديد المشاريع: {e}")
            import traceback
            print(traceback.format_exc())

    def update_selected_projects_list(self):
        """تحديث قائمة المشاريع المحددة مطابق للعملاء"""
        try:
            if not hasattr(self, 'selected_projects'):
                self.selected_projects = []

            self.selected_projects.clear()
            selected_rows = set()

            # الحصول على الصفوف المحددة
            for item in self.projects_table.selectedItems():
                selected_rows.add(item.row())

            # إضافة معرفات المشاريع المحددة
            for row in selected_rows:
                try:
                    id_item = self.projects_table.item(row, 0)
                    if id_item:
                        project_id = int(''.join(filter(str.isdigit, id_item.text())))
                        if project_id not in self.selected_projects:
                            self.selected_projects.append(project_id)
                except (ValueError, AttributeError):
                    continue

            print(f"🔍 تم تحديد {len(self.selected_projects)} مشروع: {self.selected_projects}")

        except Exception as e:
            print(f"❌ خطأ في تحديث قائمة المشاريع المحددة: {e}")

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مطابق للعملاء"""
        try:
            # إذا لم يتفاعل المستخدم مع الجدول بعد، لا نغير حالة الأزرار
            if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                print("🔥 المستخدم لم يتفاعل مع الجدول بعد - الأزرار تبقى مفعلة")
                return

            if not hasattr(self, 'selected_projects'):
                self.selected_projects = []

            selected_count = len(self.selected_projects)
            has_selection = selected_count > 0
            has_single_selection = selected_count == 1

            print(f"🔧 تحديث حالة الأزرار: {selected_count} مشروع محدد")

            # الأزرار التي تحتاج تحديد واحد فقط
            self.set_button_visibility(self.add_button, has_single_selection)
            self.set_button_visibility(self.edit_button, has_single_selection)
            self.set_button_visibility(self.view_button, has_single_selection)
            self.set_button_visibility(self.documents_button, has_single_selection)  # إدارة الوثائق

            # الأزرار التي تعمل مع التحديد المتعدد
            self.set_button_visibility(self.delete_button, has_selection)

            # تحديث نص زر الحذف
            if has_selection:
                if selected_count > 1:
                    self.delete_button.setText(f"🗑️ حذف ({selected_count})")
                else:
                    self.delete_button.setText("🗑️ حذف")

        except Exception as e:
            print(f"❌ خطأ في تحديث حالة الأزرار: {e}")

    def set_button_visibility(self, button, visible):
        """تعيين رؤية الزر مع الحفاظ على الألوان الأصلية - طريقة مبسطة"""
        try:
            if button:
                # التحقق من أن المستخدم تفاعل مع الجدول قبل تطبيق التأثيرات
                if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                    # إذا لم يتفاعل المستخدم بعد، نبقي الزر مفعلاً ومنيراً
                    button.setEnabled(True)
                    button.setGraphicsEffect(None)
                    print(f"🔥 زر {button.text()}: مفعل (لم يتفاعل المستخدم مع الجدول بعد)")
                    return

                button.setEnabled(visible)

                # طريقة أبسط وأكثر أماناً لتطبيق الشفافية
                if visible:
                    # إزالة أي تأثير شفافية
                    button.setGraphicsEffect(None)
                else:
                    # تطبيق تأثير الشفافية مع الحفاظ على الألوان
                    from PyQt5.QtWidgets import QGraphicsOpacityEffect
                    opacity_effect = QGraphicsOpacityEffect()
                    opacity_effect.setOpacity(0.4)
                    button.setGraphicsEffect(opacity_effect)

                print(f"🔧 زر {button.text()}: {'مفعل' if visible else 'معطل'}")
        except Exception as e:
            print(f"❌ خطأ في تعيين رؤية الزر: {e}")
            # في حالة فشل التأثير، استخدم الطريقة البسيطة
            if button:
                button.setEnabled(True)  # نبقيه مفعلاً في حالة الخطأ

    def setup_multi_selection(self):
        """إعداد التحديد المتعدد مطابق للموردين"""
        try:
            # تهيئة قائمة العناصر المحددة
            self.selected_items = []
            # متغير لتتبع ما إذا كان المستخدم قد تفاعل مع الجدول
            self.user_interacted_with_table = False
            print("✅ تم إعداد التحديد المتعدد للمشاريع")
        except Exception as e:
            print(f"❌ خطأ في إعداد التحديد المتعدد: {e}")

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة أزرار المشاريع...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة مشروع"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.documents_button, "📁 إدارة الوثائق"),
                (self.export_button, "📤 تصدير ▼"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    import re
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
                    print(f"🟢 تم تفعيل الزر: {name}")

            print("✅ تم تهيئة حالة أزرار المشاريع بنجاح")

            # لا نعطل الأزرار تلقائياً - تبقى مفعلة حتى يتفاعل المستخدم مع الجدول
            print("🔥 الأزرار ستبقى مفعلة حتى التفاعل مع الجدول")

        except Exception as e:
            print(f"❌ خطأ في تهيئة حالة أزرار المشاريع: {str(e)}")



    def create_advanced_projects_table(self):
        """إنشاء جدول المشاريع المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.projects_table = styled_table.table
        self.projects_table.setColumnCount(8)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🔢 ID",
            "🏗️ اسم المشروع",
            "🧑‍💼 العميل",
            "📍 الموقع",
            "🚀 تاريخ البدء",
            "🏁 تاريخ الانتهاء المتوقع",
            "📊 الحالة",
            "⚡ التقدم"
        ]
        self.projects_table.setHorizontalHeaderLabels(headers)

        # إعدادات عرض الأعمدة مع التكيف التلقائي مطابقة للعملاء
        header = self.projects_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # اسم المشروع
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # العميل
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الموقع
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # تاريخ البدء
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # تاريخ الانتهاء
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # الحالة

        # تحديد عرض الأعمدة الثابتة مطابق للعملاء
        self.projects_table.setColumnWidth(0, 120)  # ID
        self.projects_table.setColumnWidth(1, 300)  # اسم المشروع

        self.projects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.projects_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.projects_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.projects_table.setAlternatingRowColors(True)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.projects_table)
        self.setup_multi_selection()

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.projects_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.projects_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.projects_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.projects_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.projects_table.verticalHeader().setDefaultSectionSize(45)
        self.projects_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_projects_table()

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.projects_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.projects_table, event)

        self.projects_table.wheelEvent = wheelEvent

    def update_total_label(self):
        """تحديث تسمية الإجمالي"""
        try:
            total_projects = self.projects_table.rowCount()
            self.total_label.setText(f"إجمالي المشاريع: {total_projects}")
        except Exception as e:
            print(f"خطأ في تحديث إجمالي المشاريع: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات المشاريع في الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # الحصول على جميع المشاريع من قاعدة البيانات
            projects = self.session.query(Project).order_by(Project.start_date.asc(), Project.id.asc()).all()
            self.populate_table(projects)
            self.update_total_label()

        except Exception as e:
            print(f"خطأ في تحديث بيانات المشاريع: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

            # لا نستدعي معالج التحديد هنا - نتركه للمستخدم عند التفاعل مع الجدول
            print("📊 تم تحميل البيانات - الأزرار تبقى مفعلة حتى التفاعل مع الجدول")

    def populate_table(self, projects):
        """ملء جدول المشاريع بالبيانات"""
        self.projects_table.setRowCount(0)

        for row, project in enumerate(projects):
            self.projects_table.insertRow(row)

            # دالة مساعدة لإنشاء العناصر مطابق للعملاء
            def create_item(icon, text, default="No Data"):
                display_text = text if text and text.strip() else default
                item = QTableWidgetItem(f"{icon} {display_text}")
                item.setTextAlignment(Qt.AlignCenter)
                if display_text == default:
                    item.setForeground(QColor("#ef4444"))
                return item

            # الرقم مع أيقونة ثابتة - لون أسود للأرقام مطابق للعملاء
            id_item = QTableWidgetItem(f"🔢 {project.id}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
            self.projects_table.setItem(row, 0, id_item)

            self.projects_table.setItem(row, 1, create_item("🏗️", project.name))

            client_name = project.client.name if project.client else None
            self.projects_table.setItem(row, 2, create_item("🧑‍💼", client_name))
            self.projects_table.setItem(row, 3, create_item("📍", project.location))

            start_date = project.start_date.strftime("%Y-%m-%d") if project.start_date else None
            end_date = project.expected_end_date.strftime("%Y-%m-%d") if project.expected_end_date else None

            self.projects_table.setItem(row, 4, create_item("🚀", start_date))
            self.projects_table.setItem(row, 5, create_item("🏁", end_date))

            # حالة المشروع مطابق للعملاء
            status_item = QTableWidgetItem(self.get_project_status(project.status))
            status_item.setTextAlignment(Qt.AlignCenter)
            self.projects_table.setItem(row, 6, status_item)

            # شريط التقدم مع النسبة المئوية باللون الأسود
            progress = QProgressBar()
            progress_value = 0
            if project.status == 'planning':
                progress_value = 10
            elif project.status == 'in_progress':
                progress_value = 50
            elif project.status == 'completed':
                progress_value = 100
            else:
                progress_value = 0

            progress.setValue(progress_value)
            progress.setFormat(f"{progress_value}%")  # إظهار النسبة المئوية

            # تطبيق تصميم مع نص أسود
            progress.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 12px;
                    color: black;
                    background-color: #f8fafc;
                    height: 20px;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    border-radius: 6px;
                    margin: 1px;
                }
            """)

            self.projects_table.setCellWidget(row, 7, progress)

        # تحديث الإجمالي
        self.update_total_label()

    def add_watermark_to_projects_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.projects_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.projects_table.viewport())
                paint_watermark(painter, self.projects_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.projects_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.projects_table.viewport().update()
        self.projects_table.repaint()

    def get_project_status(self, status):
        """تحديد حالة المشروع مطابق للعملاء"""
        status_map = {
            'planning': '🟡 قيد التخطيط',
            'in_progress': '🟢 قيد التنفيذ',
            'completed': '🟢 مكتمل',
            'cancelled': '🔴 ملغى'
        }
        return status_map.get(status, '🟡 غير محدد')

    def filter_projects(self):
        """تصفية المشاريع بناءً على نص البحث والحالة"""
        search_text = self.search_edit.text().strip().lower()
        status = getattr(self, 'current_filter_value', None)

        # بناء الاستعلام
        query = self.session.query(Project)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Project.name.like(f"%{search_text}%") |
                Project.location.like(f"%{search_text}%") |
                Project.description.like(f"%{search_text}%")
            )

        # تطبيق تصفية الحالة
        if status:
            query = query.filter(Project.status == status)

        # تنفيذ الاستعلام
        projects = query.order_by(Project.start_date.asc(), Project.id.asc()).all()

        # تحديث الجدول
        self.populate_table(projects)

    def add_project(self):
        """إضافة مشروع جديد"""
        dialog = ProjectDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء مشروع جديد
                project = Project(**data)
                self.session.add(project)
                self.session.commit()
                self.show_success_message("تم إضافة المشروع بنجاح")
                self.refresh_data()

    def edit_project(self):
        """تعديل مشروع"""
        from utils import safe_edit_item
        from database import Project
        safe_edit_item(self, self.projects_table, Project, ProjectDialog, self.session, "مشروع")

    def delete_selected_items(self):
        """حذف المشاريع المحددة"""
        try:
            self.update_selected_items()
            if not self.selected_items:
                return

            count = len(self.selected_items)
            if count == 1:
                self.delete_project()
            else:
                if show_confirmation_message("تأكيد الحذف", f"هل تريد حذف {count} مشروع؟"):
                    for item_id in self.selected_items:
                        project = self.session.query(Project).get(item_id)
                        if project:
                            self.session.delete(project)
                    self.session.commit()
                    self.refresh_data()
        except Exception as e:
            print(f"خطأ في حذف المشاريع: {e}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية للمشاريع"""
        try:
            menu = QMenu(self)

            single_actions = [
                ("✏️ تعديل", self.edit_project),
                ("👁️ عرض التفاصيل", self.view_project_details),
                ("🗑️ حذف", self.delete_project)
            ]

            multi_actions = [
                ("🗑️ حذف {count} مشروع", self.delete_selected_items)
            ]

            self.create_context_menu_actions(menu, single_actions, multi_actions)
            menu.exec_(self.projects_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في القائمة السياقية: {e}")

    def delete_project(self):
        """حذف مشروع مع نافذة تأكيد متطورة"""
        try:
            selected_row = self.projects_table.currentRow()
            if selected_row < 0:
                self.show_warning_message("الرجاء اختيار مشروع من القائمة")
                return

            # استخراج الرقم من النص (إزالة الأيقونات)
            from utils import safe_get_id_from_table
            project_id = safe_get_id_from_table(self.projects_table, selected_row)
            if not project_id:
                self.show_error_message("لا يمكن تحديد المشروع المحدد")
                return

            project = self.session.query(Project).get(project_id)
            if not project:
                self.show_error_message("لم يتم العثور على المشروع")
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            dialog = DeleteProjectDialog(self, project)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # حذف المشروع من قاعدة البيانات
                    self.session.delete(project)
                    self.session.commit()

                    # إظهار رسالة نجاح متطورة
                    self.show_success_message(f"تم حذف المشروع '{project.name}' بنجاح")

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف المشروع: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف المشروع: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = ProjectWarningDialog(self, "تحذير", message, "⚠️")
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = ProjectSuccessDialog(self, "نجح", message, "✅")
        dialog.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = ProjectErrorDialog(self, "خطأ", message, "❌")
        dialog.exec_()

    def show_confirmation_message(self, title, message):
        """إظهار رسالة تأكيد متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = ProjectConfirmationDialog(self, title, message, "❓")
        return dialog.exec_() == QDialog.Accepted

    def view_project(self):
        """عرض تفاصيل المشروع في نافذة متطورة"""
        try:
            selected_row = self.projects_table.currentRow()
            if selected_row < 0:
                self.show_error_message("الرجاء اختيار مشروع من القائمة")
                return

            # استخراج معرف المشروع مع التعامل مع الرموز التعبيرية
            id_text = self.projects_table.item(selected_row, 0).text()
            # إزالة الرموز التعبيرية والمسافات
            import re
            project_id = int(re.sub(r'[^\d]', '', id_text))
            project = self.session.query(Project).get(project_id)

            if not project:
                self.show_error_message("لم يتم العثور على المشروع")
                return

            # إنشاء نافذة المعلومات المتطورة
            info_dialog = ProjectInfoDialog(self, project)
            info_dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض تفاصيل المشروع: {str(e)}")
            self.show_error_message(f"حدث خطأ في عرض تفاصيل المشروع: {str(e)}")

    def manage_documents(self):
        """إدارة وثائق المشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار مشروع من القائمة")
            return

        # استخراج رقم المعرف من النص (إزالة الأيقونة)
        id_text = self.projects_table.item(selected_row, 0).text()
        project_id = int(id_text.split()[-1])  # أخذ آخر جزء من النص (الرقم)
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        # فتح نافذة إدارة الوثائق الجديدة
        dialog = ProjectDocumentsDialog(self, project, self.session)
        dialog.exec_()

    def view_project_timeline(self):
        """عرض الجدول الزمني للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        # إنشاء نافذة لعرض الجدول الزمني
        dialog = QDialog(self)
        dialog.setWindowTitle(f"الجدول الزمني - {project.name}")
        dialog.setMinimumSize(700, 500)

        layout = QVBoxLayout()

        # معلومات الجدول الزمني
        status_map = {'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى'}
        progress_map = {'planning': '10%', 'in_progress': '50%', 'completed': '100%', 'cancelled': '0%'}

        timeline_text = f"""
📅 الجدول الزمني للمشروع - {project.name}

📋 التواريخ المهمة:
• تاريخ البدء: {project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد'}
• تاريخ الانتهاء المتوقع: {project.expected_end_date.strftime('%Y-%m-%d') if project.expected_end_date else 'غير محدد'}
• تاريخ الانتهاء الفعلي: {project.actual_end_date.strftime('%Y-%m-%d') if project.actual_end_date else 'لم ينته بعد'}

⏱️ المدة الزمنية:
• المدة المخططة: {(project.expected_end_date - project.start_date).days if project.start_date and project.expected_end_date else 'غير محسوبة'} يوم
• المدة الفعلية: {(project.actual_end_date - project.start_date).days if project.start_date and project.actual_end_date else 'قيد التنفيذ'} يوم

📊 حالة المشروع:
• الحالة الحالية: {status_map.get(project.status, project.status or '')}
• نسبة الإنجاز: {progress_map.get(project.status, '0%')}

📝 ملاحظات الجدولة:
• تحقق من التقدم بانتظام
• راجع المواعيد النهائية
• تواصل مع العميل عند التأخير
        """

        timeline_label = QLabel(timeline_text)
        timeline_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(timeline_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_project_budget(self):
        """عرض تفاصيل الميزانية للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        # إنشاء نافذة لعرض تفاصيل الميزانية
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تفاصيل الميزانية - {project.name}")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout()

        # معلومات الميزانية
        budget_text = f"""
💰 تفاصيل الميزانية - {project.name}

💵 الميزانية المخططة:
• إجمالي الميزانية: {format_currency(project.budget or 0)}
• الميزانية لكل متر مربع: {format_currency((project.budget or 0) / (project.area or 1)) if project.area else 'غير محسوبة'}

📊 تحليل التكاليف:
• تكلفة المواد (تقديرية): {format_currency((project.budget or 0) * 0.6)}
• تكلفة العمالة (تقديرية): {format_currency((project.budget or 0) * 0.3)}
• تكاليف أخرى (تقديرية): {format_currency((project.budget or 0) * 0.1)}

📈 مؤشرات مالية:
• حالة الميزانية: {'مناسبة' if (project.budget or 0) > 0 else 'غير محددة'}
• نوع المشروع: {'اقتصادي' if (project.budget or 0) < 100000 else 'متوسط' if (project.budget or 0) < 500000 else 'كبير'}

📝 ملاحظات مالية:
• راجع الميزانية بانتظام
• احتفظ بهامش للطوارئ (10-15%)
• وثق جميع المصروفات
        """

        budget_label = QLabel(budget_text)
        budget_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(budget_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_project_progress(self):
        """عرض تقرير التقدم للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        # إنشاء نافذة لعرض تقرير التقدم
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تقرير التقدم - {project.name}")
        dialog.setMinimumSize(650, 450)

        layout = QVBoxLayout()

        # معلومات التقدم
        progress_percentage = {'planning': 10, 'in_progress': 50, 'completed': 100, 'cancelled': 0}.get(project.status, 0)
        status_map = {'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى'}

        progress_text = f"""
📊 تقرير التقدم - {project.name}

🎯 حالة المشروع:
• الحالة: {status_map.get(project.status, project.status or '')}
• نسبة الإنجاز: {progress_percentage}%
• العميل: {project.client.name if project.client else 'غير محدد'}

📅 التقدم الزمني:
• أيام منذ البدء: {(datetime.datetime.now().date() - project.start_date.date()).days if project.start_date else 'غير محسوب'}
• أيام متبقية: {(project.expected_end_date.date() - datetime.datetime.now().date()).days if project.expected_end_date and project.expected_end_date > datetime.datetime.now() else 'انتهت المدة' if project.expected_end_date else 'غير محسوب'}

📍 معلومات الموقع:
• الموقع: {project.location or 'غير محدد'}
• المساحة: {project.area or 0} متر مربع

💰 المعلومات المالية:
• الميزانية: {format_currency(project.budget or 0)}
• التكلفة المتوقعة حتى الآن: {format_currency((project.budget or 0) * (progress_percentage / 100))}

📈 مؤشرات الأداء:
• سرعة التنفيذ: {'بطيئة' if progress_percentage < 30 else 'متوسطة' if progress_percentage < 70 else 'سريعة'}
• حالة الجدولة: {'متأخر' if project.expected_end_date and project.expected_end_date < datetime.datetime.now() and project.status != 'completed' else 'في الموعد'}

📝 التوصيات:
• {'تسريع وتيرة العمل' if progress_percentage < 50 else 'الحفاظ على الوتيرة الحالية'}
• {'مراجعة الجدولة' if project.expected_end_date and project.expected_end_date < datetime.datetime.now() else 'متابعة الخطة'}
        """

        progress_label = QLabel(progress_text)
        progress_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(progress_label)

        # شريط التقدم البصري
        progress_bar = QProgressBar()
        progress_bar.setValue(progress_percentage)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 6px;
            }
        """)
        layout.addWidget(progress_bar)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def add_document(self):
        """إضافة وثيقة للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        # فتح نافذة اختيار الملف
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار وثيقة للإضافة",
            "",
            "جميع الملفات (*);;ملفات PDF (*.pdf);;ملفات Word (*.docx *.doc);;ملفات Excel (*.xlsx *.xls);;ملفات نصية (*.txt)"
        )

        if file_path:
            # إنشاء نافذة تأكيد مع تفاصيل الملف
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            dialog = QDialog(self)
            dialog.setWindowTitle("📄 تأكيد إضافة الوثيقة")
            dialog.setModal(True)
            dialog.resize(500, 300)

            layout = QVBoxLayout()

            # معلومات الملف
            info_text = f"""
📁 تفاصيل الوثيقة:
─────────────────────────────────────────────────────────────────────────────
🏗️ المشروع: {project.name}
📄 اسم الملف: {file_name}
📊 حجم الملف: {file_size_mb:.2f} ميجابايت
📍 المسار: {file_path}

هل تريد إضافة هذه الوثيقة للمشروع؟
            """

            info_label = QLabel(info_text)
            info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
            layout.addWidget(info_label)

            # أزرار التأكيد
            buttons_layout = QHBoxLayout()

            confirm_btn = QPushButton("✅ إضافة الوثيقة")
            confirm_btn.clicked.connect(lambda: self.confirm_add_document(dialog, project, file_path, file_name))
            buttons_layout.addWidget(confirm_btn)

            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

    def confirm_add_document(self, dialog, project, file_path, file_name):
        """تأكيد إضافة الوثيقة"""
        try:
            # في التطبيق الحقيقي، ستتم إضافة الوثيقة لقاعدة البيانات
            # هنا سنعرض رسالة نجاح فقط

            dialog.close()
            self.show_success_message(f"تم إضافة الوثيقة بنجاح:\n\n📄 {file_name}\n🏗️ للمشروع: {project.name}\n\nملاحظة: في النسخة المكتملة ستتم إضافة الوثيقة فعلياً لقاعدة البيانات.")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إضافة الوثيقة: {str(e)}")

    def add_image(self):
        """إضافة صورة للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        # فتح نافذة اختيار الصورة
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار صورة للإضافة",
            "",
            "ملفات الصور (*.jpg *.jpeg *.png *.bmp *.gif);;جميع الملفات (*)"
        )

        if file_path:
            # إنشاء نافذة معاينة الصورة
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            dialog = QDialog(self)
            dialog.setWindowTitle("🖼️ معاينة وإضافة الصورة")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout()

            # معلومات الصورة
            info_text = f"""
🖼️ تفاصيل الصورة:
─────────────────────────────────────────────────────────────────────────────
🏗️ المشروع: {project.name}
📷 اسم الصورة: {file_name}
📊 حجم الملف: {file_size_mb:.2f} ميجابايت
📍 المسار: {file_path}
            """

            info_label = QLabel(info_text)
            info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
            layout.addWidget(info_label)

            # معاينة الصورة
            try:
                from PyQt5.QtGui import QPixmap
                preview_label = QLabel()
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # تصغير الصورة للمعاينة
                    scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    preview_label.setPixmap(scaled_pixmap)
                    preview_label.setAlignment(Qt.AlignCenter)
                    preview_label.setStyleSheet("border: 2px solid #ddd; border-radius: 8px; padding: 10px;")
                else:
                    preview_label.setText("❌ لا يمكن معاينة الصورة")
                    preview_label.setAlignment(Qt.AlignCenter)

                layout.addWidget(preview_label)

            except Exception as e:
                error_label = QLabel(f"❌ خطأ في معاينة الصورة: {str(e)}")
                error_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(error_label)

            # أزرار التأكيد
            buttons_layout = QHBoxLayout()

            confirm_btn = QPushButton("✅ إضافة الصورة")
            confirm_btn.clicked.connect(lambda: self.confirm_add_image(dialog, project, file_path, file_name))
            buttons_layout.addWidget(confirm_btn)

            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

    def confirm_add_image(self, dialog, project, file_path, file_name):
        """تأكيد إضافة الصورة"""
        try:
            # في التطبيق الحقيقي، ستتم إضافة الصورة لقاعدة البيانات
            # هنا سنعرض رسالة نجاح فقط

            dialog.close()
            self.show_success_message(f"تم إضافة الصورة بنجاح:\n\n🖼️ {file_name}\n🏗️ للمشروع: {project.name}\n\nملاحظة: في النسخة المكتملة ستتم إضافة الصورة فعلياً لقاعدة البيانات.")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إضافة الصورة: {str(e)}")

    def view_image_gallery(self):
        """عرض معرض صور المشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        # إنشاء نافذة معرض الصور
        dialog = QDialog(self)
        dialog.setWindowTitle(f"🖼️ معرض صور المشروع: {project.name}")
        dialog.setModal(True)
        dialog.resize(800, 600)

        layout = QVBoxLayout()

        # معلومات المشروع
        info_label = QLabel(f"🏗️ المشروع: {project.name}")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)

        # شبكة الصور التجريبية
        from PyQt5.QtWidgets import QGridLayout, QScrollArea

        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        grid_layout = QGridLayout()

        # إضافة صور تجريبية
        sample_images = [
            ("🖼️ صورة الموقع", "صورة عامة للموقع"),
            ("📷 بداية العمل", "صورة بداية المشروع"),
            ("🏗️ مرحلة البناء", "صورة أثناء التنفيذ"),
            ("✅ النتيجة النهائية", "صورة المشروع المكتمل"),
            ("📋 المخططات", "صورة المخططات"),
            ("🔧 الأدوات", "صورة الأدوات المستخدمة")
        ]

        row = 0
        col = 0
        for title, description in sample_images:
            # إنشاء بطاقة للصورة
            image_card = QFrame()
            image_card.setStyleSheet("""
                QFrame {
                    border: 2px solid #ddd;
                    border-radius: 10px;
                    padding: 10px;
                    background-color: #f9f9f9;
                }
                QFrame:hover {
                    border-color: #007bff;
                    background-color: #e3f2fd;
                }
            """)
            image_card.setFixedSize(200, 150)

            card_layout = QVBoxLayout()

            # أيقونة الصورة
            icon_label = QLabel("🖼️")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("font-size: 48px; color: #666;")
            card_layout.addWidget(icon_label)

            # عنوان الصورة
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("font-weight: bold; font-size: 12px;")
            card_layout.addWidget(title_label)

            # وصف الصورة
            desc_label = QLabel(description)
            desc_label.setAlignment(Qt.AlignCenter)
            desc_label.setStyleSheet("font-size: 10px; color: #666;")
            desc_label.setWordWrap(True)
            card_layout.addWidget(desc_label)

            image_card.setLayout(card_layout)
            grid_layout.addWidget(image_card, row, col)

            col += 1
            if col >= 3:  # 3 صور في كل صف
                col = 0
                row += 1

        scroll_widget.setLayout(grid_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_image_btn = QPushButton("📷 إضافة صورة جديدة")
        add_image_btn.clicked.connect(lambda: (dialog.close(), self.add_image()))
        buttons_layout.addWidget(add_image_btn)

        export_btn = QPushButton("📤 تصدير الصور")
        export_btn.clicked.connect(lambda: self.show_warning_message("ميزة تصدير الصور ستكون متاحة قريباً!"))
        buttons_layout.addWidget(export_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()





    def show_statistics(self):
        """عرض نافذة إحصائيات المشاريع"""
        try:
            dialog = ProjectStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض إحصائيات المشاريع: {e}")
            self.show_error_message(f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        self.columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        self.columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("🏗️ اسم المشروع", 1),
            ("🧑‍💼 العميل", 2),
            ("📍 الموقع", 3),
            ("🚀 تاريخ البدء", 4),
            ("🏁 تاريخ الانتهاء المتوقع", 5),
            ("📊 الحالة", 6),
            ("⚡ التقدم", 7)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            self.columns_menu.addAction(action)

        # إضافة فاصل
        self.columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        self.columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        self.columns_menu.addAction(hide_all_action)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            self.columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = self.columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            self.columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'projects_table') and self.projects_table:
                if visible:
                    self.projects_table.showColumn(column_index)
                else:
                    self.projects_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"خطأ في تبديل إظهار العمود: {e}")

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'projects_table') and self.projects_table:
                for i in range(self.projects_table.columnCount()):
                    self.projects_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"خطأ في إظهار جميع الأعمدة: {e}")

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'projects_table') and self.projects_table:
                for i in range(self.projects_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.projects_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"خطأ في إخفاء الأعمدة: {e}")

    def export_custom(self):
        """إظهار نافذة التصدير المخصص مطابقة للأقسام الأخرى"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel, QWidget
            from PyQt5.QtCore import Qt

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص للمشاريع - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(400, 380)  # تقليل الارتفاع

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للنافذة الحوارية: {e}")

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي مضغوط
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(15, 10, 15, 10)
            layout.setSpacing(8)

            # العنوان الرئيسي مضغوط
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(3)

            # الأيقونة والعنوان الرئيسي مضغوط
            main_title = QLabel("🔧 تصدير مخصص للمشاريع")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 22px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 5px;
                }
            """)

            # العنوان الفرعي التوضيحي مضغوط
            subtitle = QLabel("اختر البيانات المراد تصديرها بدقة")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # إنشاء المجموعات مع علامات الصح الخارجية
            self.create_project_export_groups(layout)

            # أزرار التحكم مضغوطة
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(10)

            # زر الإلغاء مطابق للبرنامج الرئيسي
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            cancel_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export_projects(dialog))
            export_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    # دوال التصدير المتقدمة الجديدة
    def export_to_excel_advanced(self):
        """تصدير Excel متقدم"""
        self.export_to_excel()

    def export_to_csv_advanced(self):
        """تصدير CSV شامل"""
        self.export_to_csv()







    def export_to_json_advanced(self):
        """تصدير JSON متقدم"""
        self.export_to_json()

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للمشاريع"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_مشاريع_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                projects = self.session.query(Project).order_by(Project.start_date.asc(), Project.id.asc()).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(projects),
                        'backup_type': 'projects_full_backup'
                    },
                    'projects': []
                }

                for project in projects:
                    project_data = {
                        'id': project.id,
                        'name': project.name,
                        'description': project.description,
                        'budget': float(project.budget) if project.budget else 0.0,
                        'status': project.status,
                        'start_date': project.start_date.isoformat() if project.start_date else None,
                        'end_date': project.end_date.isoformat() if project.end_date else None,
                        'client_id': project.client_id if project.client_id else None,
                        'client_name': project.client.name if project.client else None,
                        'created_at': project.created_at.isoformat() if hasattr(project, 'created_at') and project.created_at else None
                    }
                    backup_data['projects'].append(project_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(projects)} مشروع")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للمشاريع"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة
                reply = self.show_confirmation_message(
                    "تأكيد الاستعادة",
                    "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم استبدال البيانات الحالية!"
                )

                if reply:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    if 'projects' in backup_data:
                        restored_count = 0
                        for project_data in backup_data['projects']:
                            # التحقق من وجود المشروع
                            existing = self.session.query(Project).filter_by(id=project_data['id']).first()
                            if not existing:
                                # إنشاء مشروع جديد
                                new_project = Project(
                                    name=project_data.get('name'),
                                    description=project_data.get('description'),
                                    budget=project_data.get('budget', 0),
                                    status=project_data.get('status'),
                                    start_date=datetime.fromisoformat(project_data['start_date']).date() if project_data.get('start_date') else None,
                                    end_date=datetime.fromisoformat(project_data['end_date']).date() if project_data.get('end_date') else None,
                                    client_id=project_data.get('client_id')
                                )
                                self.session.add(new_project)
                                restored_count += 1

                        self.session.commit()
                        self.refresh_data()  # إعادة تحميل البيانات

                        self.show_success_message(f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم استعادة {restored_count} مشروع")
                    else:
                        self.show_error_message("ملف النسخة الاحتياطية غير صالح!")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def create_project_export_groups(self, layout):
        """إنشاء مجموعات التصدير للمشاريع مع علامات الصح الخارجية"""
        try:
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox
            from PyQt5.QtCore import Qt

            # مجموعة البيانات الأساسية
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)
            basic_main_layout.setContentsMargins(15, 5, 15, 5)

            basic_title = QLabel("🏗️ البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)

            # إنشاء عناصر CheckBox للمشاريع
            self.export_id = QCheckBox("🆔 الرقم التعريفي")
            self.export_name = QCheckBox("🏗️ اسم المشروع")
            self.export_budget = QCheckBox("💰 الميزانية")
            self.export_status = QCheckBox("📊 الحالة")
            self.export_client = QCheckBox("👤 العميل")

            # تحديد افتراضي
            self.export_name.setChecked(True)
            self.export_budget.setChecked(True)

            checkboxes_data = [
                (self.export_id, "#3B82F6"),
                (self.export_name, "#10B981"),
                (self.export_budget, "#F59E0B"),
                (self.export_status, "#EF4444"),
                (self.export_client, "#8B5CF6")
            ]

            for checkbox, color in checkboxes_data:
                self.create_checkbox_item(checkbox, color, basic_layout)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)

            # مجموعة البيانات المالية
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)
            financial_main_layout.setContentsMargins(15, 5, 15, 5)

            financial_title = QLabel("💰 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)

            self.export_cost = QCheckBox("💵 التكلفة الفعلية")
            self.export_profit = QCheckBox("📈 الربح المتوقع")
            self.export_payment_status = QCheckBox("💳 حالة الدفع")

            self.export_cost.setChecked(True)

            financial_checkboxes_data = [
                (self.export_cost, "#10B981"),
                (self.export_profit, "#F59E0B"),
                (self.export_payment_status, "#EF4444")
            ]

            for checkbox, color in financial_checkboxes_data:
                self.create_checkbox_item(checkbox, color, financial_layout)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)

            # مجموعة البيانات الإضافية
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)
            additional_main_layout.setContentsMargins(15, 5, 15, 5)

            additional_title = QLabel("📝 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)

            self.export_description = QCheckBox("📋 الوصف")
            self.export_start_date = QCheckBox("📅 تاريخ البداية")
            self.export_end_date = QCheckBox("📅 تاريخ النهاية")
            self.export_statistics = QCheckBox("📊 إضافة الإحصائيات")

            additional_checkboxes_data = [
                (self.export_description, "#8B5CF6"),
                (self.export_start_date, "#F59E0B"),
                (self.export_end_date, "#EF4444"),
                (self.export_statistics, "#3B82F6")
            ]

            for checkbox, color in additional_checkboxes_data:
                self.create_checkbox_item(checkbox, color, additional_layout)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

        except Exception as e:
            print(f"خطأ في إنشاء مجموعات التصدير: {e}")

    def create_checkbox_item(self, checkbox, color, parent_layout):
        """إنشاء عنصر CheckBox مع علامة صح خارجية - مطابق للأقسام الأخرى"""
        try:
            from PyQt5.QtWidgets import QWidget, QHBoxLayout, QLabel
            from PyQt5.QtCore import Qt

            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 2px;
                    margin: 0px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(8)
            item_layout.setContentsMargins(8, 3, 8, 3)

            checkbox.setStyleSheet(f"""
                QCheckBox {{
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: normal;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    spacing: 12px;
                }}
                QCheckBox::indicator {{
                    width: 20px;
                    height: 20px;
                    border: 2px solid {color};
                    border-radius: 4px;
                    background: rgba(255, 255, 255, 0.1);
                }}
                QCheckBox::indicator:checked {{
                    background: {color};
                    border: 2px solid #ffffff;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                }}
                QCheckBox::indicator:hover {{
                    background: rgba(255, 255, 255, 0.2);
                    border: 2px solid rgba(255, 255, 255, 0.8);
                }}
            """)

            check_label = QLabel("")
            check_label.setFixedSize(30, 25)
            check_label.setAlignment(Qt.AlignCenter)
            check_label.setStyleSheet("""
                QLabel {
                    color: #10B981;
                    font-size: 20px;
                    font-weight: bold;
                    background: rgba(16, 185, 129, 0.1);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 8px;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            def create_toggle_function(label):
                def toggle_check(state):
                    if state == 2:  # محدد
                        label.setText("✓")
                    else:
                        label.setText("")
                return toggle_check

            checkbox.stateChanged.connect(create_toggle_function(check_label))

            if checkbox.isChecked():
                check_label.setText("✓")

            item_layout.addWidget(check_label)
            item_layout.addWidget(checkbox)
            parent_layout.addWidget(item_widget)

        except Exception as e:
            print(f"خطأ في إنشاء عنصر CheckBox: {e}")

    def perform_custom_export_projects(self, dialog):
        """تنفيذ التصدير المخصص للمشاريع"""
        try:
            import csv
            from datetime import datetime
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_مشاريع_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                projects = self.session.query(Project).order_by(Project.start_date.asc(), Project.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    if self.export_statistics.isChecked():
                        writer.writerow(['تصدير مخصص للمشاريع'])
                        writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                        writer.writerow([f'إجمالي المشاريع: {len(projects)}'])
                        writer.writerow([])

                    # إنشاء رؤوس الأعمدة
                    headers = []
                    if self.export_id.isChecked():
                        headers.append('الرقم التعريفي')
                    if self.export_name.isChecked():
                        headers.append('اسم المشروع')
                    if self.export_budget.isChecked():
                        headers.append('الميزانية')
                    if self.export_status.isChecked():
                        headers.append('الحالة')
                    if self.export_client.isChecked():
                        headers.append('العميل')
                    if self.export_cost.isChecked():
                        headers.append('التكلفة الفعلية')
                    if self.export_profit.isChecked():
                        headers.append('الربح المتوقع')
                    if self.export_payment_status.isChecked():
                        headers.append('حالة الدفع')
                    if self.export_description.isChecked():
                        headers.append('الوصف')
                    if self.export_start_date.isChecked():
                        headers.append('تاريخ البداية')
                    if self.export_end_date.isChecked():
                        headers.append('تاريخ النهاية')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for project in projects:
                        row = []
                        if self.export_id.isChecked():
                            row.append(project.id)
                        if self.export_name.isChecked():
                            row.append(project.name or 'غير محدد')
                        if self.export_budget.isChecked():
                            row.append(f"{project.budget:.2f}" if project.budget else '0.00')
                        if self.export_status.isChecked():
                            row.append(project.status or 'غير محدد')
                        if self.export_client.isChecked():
                            client_name = project.client.name if project.client else 'غير محدد'
                            row.append(client_name)
                        if self.export_cost.isChecked():
                            row.append(f"{project.cost:.2f}" if hasattr(project, 'cost') and project.cost else '0.00')
                        if self.export_profit.isChecked():
                            profit = (project.budget - project.cost) if hasattr(project, 'cost') and project.budget and project.cost else 0
                            row.append(f"{profit:.2f}")
                        if self.export_payment_status.isChecked():
                            row.append('مدفوع' if project.status == 'مكتمل' else 'غير مدفوع')
                        if self.export_description.isChecked():
                            row.append(project.description or 'لا يوجد وصف')
                        if self.export_start_date.isChecked():
                            row.append(project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد')
                        if self.export_end_date.isChecked():
                            row.append(project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد')

                        writer.writerow(row)

                dialog.accept()
                self.show_success_message(f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                },
                'purple': {
                    'bg_start': '#581c87', 'bg_mid': '#7c3aed', 'bg_end': '#8b5cf6', 'bg_bottom': '#a855f7',
                    'hover_start': '#a855f7', 'hover_mid': '#c084fc', 'hover_end': '#d8b4fe', 'hover_bottom': '#e9d5ff',
                    'hover_border': '#c084fc', 'pressed_start': '#3b0764', 'pressed_mid': '#581c87',
                    'pressed_end': '#6b21a8', 'pressed_bottom': '#7c3aed', 'pressed_border': '#6b21a8',
                    'border': '#a855f7', 'text': '#ffffff', 'shadow': 'rgba(168, 85, 247, 0.6)'
                },
                'gray': {
                    'bg_start': '#2d3748', 'bg_mid': '#4a5568', 'bg_end': '#718096', 'bg_bottom': '#a0aec0',
                    'hover_start': '#a0aec0', 'hover_mid': '#cbd5e0', 'hover_end': '#e2e8f0', 'hover_bottom': '#f7fafc',
                    'hover_border': '#cbd5e0', 'pressed_start': '#1a202c', 'pressed_mid': '#2d3748',
                    'pressed_end': '#4a5568', 'pressed_bottom': '#718096', 'pressed_border': '#4a5568',
                    'border': '#718096', 'text': '#ffffff', 'shadow': 'rgba(113, 128, 150, 0.8)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#2d2d2d', 'bg_bottom': '#404040',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#1a1a1a', 'pressed_bottom': '#2d2d2d', 'pressed_border': '#1a1a1a',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                },
                'warning': {
                    'bg_start': '#451a03', 'bg_mid': '#78350f', 'bg_end': '#a16207', 'bg_bottom': '#eab308',
                    'hover_start': '#78350f', 'hover_mid': '#ca8a04', 'hover_end': '#eab308', 'hover_bottom': '#facc15',
                    'hover_border': '#eab308', 'pressed_start': '#451a03', 'pressed_mid': '#78350f',
                    'pressed_end': '#a16207', 'pressed_bottom': '#ca8a04', 'pressed_border': '#a16207',
                    'border': '#eab308', 'text': '#ffffff', 'shadow': 'rgba(234, 179, 8, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def export_to_excel(self):
        """تصدير المشاريع إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير المشاريع إلى CSV"""
        try:
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", "المشاريع.csv", "CSV Files (*.csv)"
            )

            if file_path:
                projects = self.session.query(Project).order_by(Project.start_date.asc(), Project.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'اسم المشروع', 'العميل', 'التاريخ', 'الحالة', 'الميزانية', 'الوصف'])

                    # كتابة البيانات
                    for project in projects:
                        client_name = project.client.name if project.client else "غير محدد"
                        date_str = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""

                        writer.writerow([
                            project.id,
                            project.name,
                            client_name,
                            date_str,
                            project.status or "",
                            project.budget or 0,
                            project.description or ""
                        ])

                self.show_success_message(f"تم تصدير المشاريع بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المشاريع إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            projects = self.session.query(Project).order_by(Project.start_date.asc(), Project.id.asc()).all()

            if not projects:
                self.show_warning_message("لا توجد مشاريع للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المشاريع", "تقرير_المشاريع.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المشاريع</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #2563eb; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>🏗️ تقرير المشاريع</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم المشروع</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>الميزانية</th>
                        </tr>
                """

                for project in projects:
                    client_name = project.client.name if project.client else "غير محدد"
                    date_str = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""
                    budget = int(project.budget) if project.budget else 0

                    html_content += f"""
                        <tr>
                            <td>{project.id}</td>
                            <td>{project.name}</td>
                            <td>{client_name}</td>
                            <td>{date_str}</td>
                            <td>{project.status or ""}</td>
                            <td>{budget:,} جنيه</td>
                        </tr>
                    """

                html_content += """
                    </table>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                self.show_success_message(f"تم تصدير المشاريع إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير المشاريع إلى JSON"""
        try:
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المشاريع.json", "JSON Files (*.json)"
            )

            if file_path:
                projects = self.session.query(Project).order_by(Project.start_date.asc(), Project.id.asc()).all()

                projects_data = []
                for project in projects:
                    project_data = {
                        'id': project.id,
                        'name': project.name,
                        'client': project.client.name if project.client else None,
                        'start_date': project.start_date.strftime("%Y-%m-%d") if project.start_date else None,
                        'status': project.status,
                        'budget': float(project.budget) if project.budget else 0,
                        'description': project.description
                    }
                    projects_data.append(project_data)

                export_data = {
                    "export_info": {
                        "export_date": QDate.currentDate().toString('yyyy-MM-dd'),
                        "total_projects": len(projects_data),
                        "exported_by": "نظام إدارة المشاريع"
                    },
                    "projects": projects_data
                }

                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم تصدير المشاريع بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير: {str(e)}")

    def export_documents(self):
        """تصدير قائمة الوثائق"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            self.show_warning_message("الرجاء اختيار مشروع لتصدير وثائقه")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            self.show_error_message("لم يتم العثور على المشروع")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ قائمة الوثائق", f"وثائق_{project.name}.txt", "Text Files (*.txt)"
            )

            if file_path:
                # إنشاء قائمة الوثائق
                documents_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                            📁 قائمة وثائق المشروع
═══════════════════════════════════════════════════════════════════════════════

🏗️ اسم المشروع: {project.name}
🧑‍💼 العميل: {project.client.name if project.client else 'غير محدد'}
📅 تاريخ التصدير: {QDate.currentDate().toString('yyyy-MM-dd')}

📋 الوثائق المتوفرة:
─────────────────────────────────────────────────────────────────────────────
📄 مخطط المشروع.pdf
📄 العقد الأساسي.docx
🖼️ صورة الموقع.jpg
📊 تقرير التقدم.xlsx
📋 قائمة المهام.txt
📝 ملاحظات المشروع.docx
💰 تفاصيل الميزانية.xlsx

📊 ملخص:
─────────────────────────────────────────────────────────────────────────────
• إجمالي الوثائق: 7
• الوثائق النصية: 4
• الصور: 1
• جداول البيانات: 2

ملاحظة: هذه قائمة تجريبية للوثائق. في النسخة المكتملة ستعرض الوثائق الفعلية المرفوعة.
"""

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(documents_content)

                self.show_success_message(f"تم تصدير قائمة الوثائق بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير الوثائق: {str(e)}")

    def export_detailed_report(self):
        """تصدير تقرير تفصيلي للمشاريع"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            # الحصول على جميع المشاريع
            projects = self.session.query(Project).order_by(Project.start_date.asc(), Project.id.asc()).all()

            if not projects:
                self.show_warning_message("لا توجد مشاريع للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير التفصيلي",
                f"تقرير_مشاريع_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow([
                        'الرقم', 'اسم المشروع', 'العميل', 'الموقع', 'المساحة',
                        'تاريخ البدء', 'تاريخ الانتهاء المتوقع', 'الحالة', 'الميزانية', 'الملاحظات'
                    ])

                    # كتابة البيانات التفصيلية
                    total_budget = 0
                    for project in projects:
                        client_name = project.client.name if project.client else "غير محدد"
                        status_map = {
                            "planning": "قيد التخطيط",
                            "in_progress": "قيد التنفيذ",
                            "completed": "مكتمل",
                            "cancelled": "ملغى",
                            "on_hold": "معلق",
                            "review": "قيد المراجعة",
                            "approved": "معتمد"
                        }
                        status_text = status_map.get(project.status, project.status or "غير محدد")

                        writer.writerow([
                            project.id,
                            project.name,
                            client_name,
                            project.location or "غير محدد",
                            f"{project.area:.0f} م²" if project.area else "غير محدد",
                            project.start_date.strftime('%Y-%m-%d') if project.start_date else "",
                            project.expected_end_date.strftime('%Y-%m-%d') if project.expected_end_date else "",
                            status_text,
                            f"{project.budget:.2f}" if project.budget else "0",
                            project.notes or ""
                        ])
                        total_budget += project.budget or 0

                    # إضافة صف الإجمالي
                    writer.writerow([])
                    writer.writerow(['إجمالي الميزانيات', '', '', '', '', '', '', '', f"{total_budget:.2f}", ''])

                self.show_success_message(f"تم تصدير التقرير التفصيلي بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التقرير التفصيلي: {str(e)}")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة للمشاريع"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            from sqlalchemy import func

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الأرصدة",
                f"تقرير_أرصدة_مشاريع_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow(['نوع التقرير', 'المبلغ', 'العدد', 'النسبة المئوية'])
                    writer.writerow([])

                    # تقرير حسب الحالة
                    writer.writerow(['=== تقرير حسب الحالة ==='])
                    status_map = {
                        "planning": "قيد التخطيط",
                        "in_progress": "قيد التنفيذ",
                        "completed": "مكتمل",
                        "cancelled": "ملغى",
                        "on_hold": "معلق",
                        "review": "قيد المراجعة",
                        "approved": "معتمد"
                    }

                    total_budget = self.session.query(func.sum(Project.budget)).scalar() or 0
                    total_count = self.session.query(func.count(Project.id)).scalar() or 0

                    for status_key, status_name in status_map.items():
                        result = self.session.query(
                            func.sum(Project.budget),
                            func.count(Project.id)
                        ).filter(Project.status == status_key).first()

                        budget = result[0] or 0
                        count = result[1] or 0
                        percentage = (budget / total_budget * 100) if total_budget > 0 else 0

                        writer.writerow([status_name, f"{budget:.2f}", count, f"{percentage:.1f}%"])

                    writer.writerow(['الإجمالي', f"{total_budget:.2f}", total_count, "100.0%"])
                    writer.writerow([])

                    # تقرير حسب العملاء
                    writer.writerow(['=== تقرير حسب العملاء ==='])
                    clients = self.session.query(
                        Client.name,
                        func.sum(Project.budget),
                        func.count(Project.id)
                    ).join(Project).group_by(Client.name).all()

                    for client_name, budget, count in clients:
                        percentage = (budget / total_budget * 100) if total_budget > 0 else 0
                        writer.writerow([client_name, f"{budget:.2f}", count, f"{percentage:.1f}%"])

                self.show_success_message(f"تم تصدير تقرير الأرصدة بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير تقرير الأرصدة: {str(e)}")


class ProjectInfoDialog(QDialog):
    """نافذة عرض تفاصيل المشروع - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, project=None):
        super().__init__(parent)
        self.project = project
        self.parent_widget = parent
        self.setup_ui()

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي الموحد"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """

    def setup_ui(self):
        """إعداد واجهة النافذة - مطابق تماماً للنموذج المرجعي"""
        self.setWindowTitle(f"🏗️ {self.project.name if self.project.name else 'مشروع'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(900, 700)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(self.get_reference_styling())

        # تخطيط رئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف
        title_text = QLabel(f"🏗️ تفاصيل المشروع: {self.project.name}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # إنشاء منطقة التمرير المحسنة للمعلومات - مطابقة تماماً للنموذج المرجعي
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(71, 85, 105, 0.4),
                    stop:1 rgba(100, 116, 139, 0.3));
                width: 16px;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.5),
                    stop:1 rgba(34, 197, 94, 0.4));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 7px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(34, 197, 94, 0.6));
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:0.5 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(21, 128, 61, 0.7));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق تماماً للنموذج المرجعي
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)

        # إضافة معلومات المشروع
        self.add_project_info(info_layout)

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def add_project_info(self, layout):
        """إضافة معلومات المشروع إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.project:
            return

        # قسم المعلومات الأساسية والشخصية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.project.id).zfill(8)}"),
            ("👤 العميل", self.project.client.name if self.project.client else "غير محدد"),
            ("📍 الموقع الكامل", self.project.location or "غير محدد"),
            ("🏷️ حالة المشروع", self.get_project_status()),
            ("📊 مستوى البيانات", self.get_data_completeness())
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية والشخصية", basic_info)

        # قسم المعلومات المالية المحسن
        budget_color = self.get_budget_color()
        budget_text = f"{self.project.budget:.0f} جنيه" if self.project.budget else "0 جنيه"
        self.add_info_section(layout, "💰 المعلومات المالية", [
            ("💵 الميزانية الإجمالية", f"{budget_color} {budget_text}"),
            ("📊 حالة الميزانية", self.get_budget_status()),
            ("💳 تصنيف المشروع", self.get_project_type()),
            ("📈 مستوى الأولوية", self.get_priority_level()),
            ("⚖️ تقييم المخاطر", self.get_risk_assessment())
        ])

        # قسم معلومات التقدم والجدولة
        self.add_info_section(layout, "📅 معلومات التقدم والجدولة", [
            ("📅 تاريخ البدء", self.get_start_date()),
            ("⏰ تاريخ الانتهاء المتوقع", self.get_expected_end_date()),
            ("📈 مدة المشروع", self.get_project_duration()),
            ("📊 نسبة الإنجاز", self.get_completion_percentage()),
            ("💼 الوقت المتبقي", self.get_remaining_time())
        ])

        # قسم معلومات المساحة والمواصفات
        self.add_info_section(layout, "📐 معلومات المساحة والمواصفات", [
            ("📐 المساحة الإجمالية", f"{self.project.area or 0} متر مربع"),
            ("🏗️ نوع المشروع", self.get_project_category()),
            ("🔧 المواصفات التقنية", self.get_technical_specs()),
            ("📋 متطلبات خاصة", self.get_special_requirements())
        ])

        # قسم الملاحظات والتفاصيل الإضافية
        self.add_info_section(layout, "📝 ملاحظات وتفاصيل إضافية", [
            ("📝 الوصف", self.project.description or "لا يوجد وصف"),
            ("📝 الملاحظات", self.project.notes or "لا توجد ملاحظات"),
            ("🔍 معلومات إضافية", self.get_additional_info()),
            ("📋 ملخص المشروع", self.get_project_summary())
        ])

    def add_info_section(self, layout, title, items):
        """إضافة قسم معلومات محسن مع تصميم متطور - مطابق تماماً للنموذج المرجعي"""
        # إطار القسم الرئيسي
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(241, 245, 249, 0.10),
                    stop:1 rgba(226, 232, 240, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 15px;
                margin: 8px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المحسن
        section_title = QLabel(title)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(34, 197, 94, 0.2));
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 12px;
                padding: 12px 20px;
                margin-bottom: 10px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(section_title)

        # عناصر القسم المحسنة
        for i, (label, value) in enumerate(items):
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(12, 8, 12, 8)
            item_layout.setSpacing(15)

            # خلفية متدرجة للعناصر
            item_widget.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.03 + (i % 2) * 0.02}),
                        stop:0.5 rgba(248, 250, 252, {0.05 + (i % 2) * 0.02}),
                        stop:1 rgba(241, 245, 249, {0.03 + (i % 2) * 0.02}));
                    border: 1px solid rgba(255, 255, 255, 0.12);
                    border-radius: 10px;
                    margin: 2px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.08),
                        stop:0.5 rgba(139, 92, 246, 0.06),
                        stop:1 rgba(34, 197, 94, 0.05));
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }}
            """)

            # التسمية المحسنة - نصوص أوضح
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: 2px solid rgba(255, 255, 255, 0.25);
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة مع ألوان مخصصة
            value_widget = QLabel(str(value))

            # تحديد لون القيمة حسب المحتوى
            value_color = self.get_value_color(label, str(value))

            value_widget.setStyleSheet(f"""
                QLabel {{
                    color: {value_color};
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: 1px solid rgba(255, 255, 255, 0.18);
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }}
                QLabel:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.5 rgba(139, 92, 246, 0.12),
                        stop:1 rgba(34, 197, 94, 0.10));
                    border: 1px solid rgba(255, 255, 255, 0.25);
                }}
            """)
            value_widget.setWordWrap(True)

            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget, 1)

            section_layout.addWidget(item_widget)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - ألوان أوضح وأكثر تباينًا"""
        colors = {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'success': '#32CD32',       # أخضر ليموني للنجاح
            'error': '#FF4500',         # برتقالي أحمر للأخطاء
            'special': '#DA70D6',       # بنفسجي نيون للمميز
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

        # تحديد اللون حسب التسمية والقيمة
        if "ميزانية" in label.lower() or "مالي" in label.lower():
            if "🟢" in value or "جيد" in value:
                return colors['positive']
            elif "🔴" in value or "ضعيف" in value:
                return colors['negative']
            else:
                return colors['neutral']
        elif "حالة" in label.lower():
            if "مكتمل" in value or "✅" in value:
                return colors['success']
            elif "ملغي" in value or "❌" in value:
                return colors['error']
            elif "قيد التنفيذ" in value:
                return colors['warning']
            else:
                return colors['info']
        elif "تصنيف" in label.lower() or "مستوى" in label.lower():
            if "مميز" in value or "عالي" in value or "⭐" in value:
                return colors['special']
            elif "متوسط" in value:
                return colors['warning']
            else:
                return colors['neutral']
        elif "غير محدد" in value or "لا توجد" in value:
            return colors['neutral']
        else:
            return colors['default']

    def get_budget_color(self):
        """تحديد لون الميزانية"""
        if not self.project.budget or self.project.budget == 0:
            return "🟡"  # أصفر للصفر
        elif self.project.budget > 100000:
            return "🟢"  # أخضر للميزانية الكبيرة
        else:
            return "🔵"  # أزرق للميزانية العادية

    def get_project_status(self):
        """حالة المشروع"""
        status_map = {
            'planning': '📋 قيد التخطيط',
            'in_progress': '🔄 قيد التنفيذ',
            'completed': '✅ مكتمل',
            'cancelled': '❌ ملغي',
            'on_hold': '⏸️ متوقف مؤقتاً',
            'review': '🔍 قيد المراجعة',
            'approved': '✅ معتمد'
        }
        return status_map.get(self.project.status, self.project.status or "غير محدد")

    def get_data_completeness(self):
        """مستوى اكتمال البيانات"""
        fields = [self.project.name, self.project.location, self.project.description,
                 self.project.budget, self.project.client_id]
        filled = sum(1 for field in fields if field)
        percentage = (filled / len(fields)) * 100
        if percentage == 100:
            return f"مكتمل {percentage:.0f}% ✅"
        elif percentage >= 75:
            return f"جيد {percentage:.0f}% 🟢"
        elif percentage >= 50:
            return f"متوسط {percentage:.0f}% 🟡"
        else:
            return f"ناقص {percentage:.0f}% 🔴"

    def get_budget_status(self):
        """حالة الميزانية"""
        if not self.project.budget or self.project.budget == 0:
            return "غير محددة ⚪"
        elif self.project.budget > 500000:
            return "ميزانية كبيرة 💰"
        elif self.project.budget > 100000:
            return "ميزانية متوسطة 💵"
        else:
            return "ميزانية صغيرة 💴"

    def get_project_type(self):
        """تصنيف المشروع"""
        budget = self.project.budget if self.project.budget else 0
        if budget > 1000000:
            return "مشروع ضخم 🌟"
        elif budget > 500000:
            return "مشروع كبير ⭐"
        elif budget > 100000:
            return "مشروع متوسط 📊"
        else:
            return "مشروع صغير 📋"

    def get_priority_level(self):
        """مستوى الأولوية"""
        if self.project.status == 'in_progress':
            return "أولوية عالية 🔥"
        elif self.project.status == 'planning':
            return "أولوية متوسطة 📊"
        else:
            return "أولوية منخفضة 📉"

    def get_risk_assessment(self):
        """تقييم المخاطر"""
        if self.project.status == 'cancelled':
            return "مخاطر عالية ⚠️"
        elif self.project.status == 'on_hold':
            return "مخاطر متوسطة 🟡"
        else:
            return "مخاطر منخفضة ✅"

    def get_start_date(self):
        """تاريخ البدء"""
        if self.project.start_date:
            return self.project.start_date.strftime('%Y-%m-%d')
        return "غير محدد"

    def get_expected_end_date(self):
        """تاريخ الانتهاء المتوقع"""
        if self.project.expected_end_date:
            return self.project.expected_end_date.strftime('%Y-%m-%d')
        return "غير محدد"

    def get_project_duration(self):
        """مدة المشروع"""
        if self.project.start_date and self.project.expected_end_date:
            duration = (self.project.expected_end_date - self.project.start_date).days
            if duration < 30:
                return f"{duration} يوم"
            elif duration < 365:
                months = duration // 30
                return f"{months} شهر"
            else:
                years = duration // 365
                return f"{years} سنة"
        return "غير محسوب"

    def get_completion_percentage(self):
        """نسبة الإنجاز"""
        if self.project.status == 'completed':
            return "100% ✅"
        elif self.project.status == 'in_progress':
            return "50% 🔄"
        elif self.project.status == 'planning':
            return "10% 📋"
        else:
            return "0% ⏸️"

    def get_remaining_time(self):
        """الوقت المتبقي"""
        if self.project.expected_end_date:
            from datetime import datetime
            remaining = (self.project.expected_end_date - datetime.now()).days
            if remaining > 0:
                return f"{remaining} يوم متبقي"
            elif remaining == 0:
                return "ينتهي اليوم"
            else:
                return f"تأخر {abs(remaining)} يوم"
        return "غير محسوب"

    def get_project_category(self):
        """نوع المشروع"""
        area = self.project.area if self.project.area else 0
        if area > 1000:
            return "مشروع كبير المساحة 🏢"
        elif area > 500:
            return "مشروع متوسط المساحة 🏠"
        elif area > 0:
            return "مشروع صغير المساحة 🏘️"
        else:
            return "غير محدد المساحة"

    def get_technical_specs(self):
        """المواصفات التقنية"""
        return "مواصفات قياسية"

    def get_special_requirements(self):
        """متطلبات خاصة"""
        return "لا توجد متطلبات خاصة"

    def get_additional_info(self):
        """معلومات إضافية"""
        info_parts = []
        if self.project.start_date:
            from datetime import datetime
            days_since_start = (datetime.now() - self.project.start_date).days
            info_parts.append(f"مضى {days_since_start} يوم على البدء")

        if self.project.budget:
            if self.project.budget > 100000:
                info_parts.append(f"ميزانية كبيرة: {self.project.budget:.0f} جنيه")

        return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

    def get_project_summary(self):
        """ملخص المشروع"""
        status = self.get_project_status()
        budget = self.project.budget if self.project.budget else 0
        return f"المشروع {status} بميزانية {budget:.0f} جنيه"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;
                margin: 5px 0;
                min-height: 65px;
                max-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الإغلاق - في المقدمة
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)
        close_btn.setMaximumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)
        print_btn.setMaximumHeight(45)
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)
        export_pdf_btn.setMaximumHeight(45)
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)
        note_btn.setMaximumHeight(45)
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار
        buttons_layout.addWidget(close_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                color_pair = colors.get(button_type, ('#6B7280', '#9CA3AF'))

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[1]});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[0]});
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات المشروع"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y = 100

                # العنوان الرئيسي
                painter.setFont(title_font)
                painter.drawText(100, y, f"تقرير تفصيلي للمشروع: {self.project.name}")
                y += 80

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات الأساسية:")
                y += 40

                painter.setFont(normal_font)
                basic_info = [
                    f"المعرف: #{str(self.project.id).zfill(6)}",
                    f"الاسم: {self.project.name}",
                    f"العميل: {self.project.client.name if self.project.client else 'غير محدد'}",
                    f"الموقع: {self.project.location or 'غير محدد'}",
                    f"المساحة: {self.project.area or 0} متر مربع",
                    f"الميزانية: {self.project.budget or 0} جنيه"
                ]

                for line in basic_info:
                    painter.drawText(120, y, line)
                    y += 30

                painter.end()

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات المشروع إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QColor, QPen
            from PyQt5.QtCore import QRect
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير معلومات المشروع إلى PDF",
                f"project_{self.project.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if not filename:
                return

            printer = QPrinter()
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(filename)
            printer.setPageSize(QPrinter.A4)

            painter = QPainter()
            painter.begin(printer)

            title_font = QFont("Arial", 18, QFont.Bold)
            subtitle_font = QFont("Arial", 14, QFont.Bold)
            content_font = QFont("Arial", 12)

            page_rect = printer.pageRect()
            margin = 50
            content_rect = QRect(margin, margin,
                               page_rect.width() - 2*margin,
                               page_rect.height() - 2*margin)

            y_pos = content_rect.top()

            # رسم العنوان الرئيسي
            painter.setFont(title_font)
            painter.setPen(QColor(0, 0, 0))
            title_text = f"🏗️ معلومات المشروع: {self.project.name}"
            painter.drawText(content_rect.left(), y_pos, title_text)
            y_pos += 70

            # رسم خط فاصل
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 50

            # المعلومات الأساسية
            painter.setFont(subtitle_font)
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(content_rect.left(), y_pos, "المعلومات الأساسية:")
            y_pos += 40

            painter.setFont(content_font)
            painter.setPen(QColor(50, 50, 50))

            basic_info = [
                f"معرف المشروع: {self.project.id}",
                f"الاسم: {self.project.name}",
                f"العميل: {self.project.client.name if self.project.client else 'غير محدد'}",
                f"الموقع: {self.project.location or 'غير محدد'}",
                f"المساحة: {self.project.area or 0} متر مربع",
                f"الميزانية: {self.project.budget or 0} جنيه",
                f"الحالة: {self.get_project_status()}"
            ]

            for line in basic_info:
                painter.drawText(content_rect.left() + 20, y_pos, line)
                y_pos += 30

            painter.end()

            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message(f"تم تصدير معلومات المشروع إلى:\n{filename}")
            else:
                from utils import show_info_message
                show_info_message("نجح", f"تم تصدير معلومات المشروع إلى:\n{filename}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في التصدير: {str(e)}")
            else:
                from utils import show_error_message
                show_error_message("خطأ", f"فشل في التصدير: {str(e)}")

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddProjectNoteDialog(self, self.project, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_project_info()
        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_project_info(self):
        """تحديث معلومات المشروع"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                updated_project = self.parent_widget.session.query(Project).get(self.project.id)
                if updated_project:
                    self.project = updated_project
                    self.setup_ui()
        except Exception as e:
            print(f"خطأ في تحديث معلومات المشروع: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان - موحد مع باقي النوافذ"""
        try:
            # إنشاء أيقونة مخصصة متطورة مع تدرجات جديدة
            pixmap = QPixmap(48, 48)  # حجم أكبر للوضوح
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج متطور جديد للمشاريع
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(34, 197, 94))    # أخضر زمردي
            gradient.setColorAt(0.7, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(1, QColor(139, 92, 246))    # بنفسجي

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم الأيقونة
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🏗️")
            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور - موحد مع باقي النوافذ"""
        try:
            # استخدام نفس TitleBarStyler المستخدم في الموردين
            from ui.suppliers import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            try:
                # محاولة بديلة
                from ui.title_bar_styler import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(self)
            except Exception as e2:
                print(f"خطأ في تطبيق تصميم شريط العنوان: {e2}")


class AddProjectNoteDialog(QDialog):
    """نافذة ملاحظات المشروع - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, project=None, parent_widget=None):
        super().__init__(parent)
        self.project = project
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً - مطابق للنموذج المرجعي"""
        self.setWindowTitle(f"📝 {self.project.name if self.project.name else 'مشروع'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(ProjectInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف - مكبر ومرفوع
        title_text = QLabel(f"📝 ملاحظة للمشروع: {self.project.name}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        # وضع النص في المنتصف تماماً
        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # محرر النصوص المتطور مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(248, 250, 252, 0.1));
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', Arial, sans-serif;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                selection-background-color: rgba(59, 130, 246, 0.5);
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(255, 215, 0, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:1 rgba(248, 250, 252, 0.15));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن المشروع، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        cancel_btn.setMinimumHeight(45)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        save_btn.setMinimumHeight(45)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور"""
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['bg_start']},
                        stop:0.15 {color_set['bg_mid']},
                        stop:0.85 {color_set['bg_end']},
                        stop:1 {color_set['bg_bottom']});
                    color: #ffffff;
                    border: 4px solid {color_set['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['hover_start']},
                        stop:0.15 {color_set['hover_mid']},
                        stop:0.85 {color_set['hover_end']},
                        stop:1 {color_set['hover_bottom']});
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3);
                }}
                QPushButton:pressed {{
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_set['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.2),
                               inset 0 -1px 0 rgba(255, 255, 255, 0.2);
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة الحالية"""
        if self.project and self.project.notes:
            self.text_editor.setPlainText(self.project.notes)

    def save_note(self):
        """حفظ الملاحظة - مطابق للنموذج المرجعي"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.project.notes = note if note else None

            if self.parent_widget and self.parent_widget.session:
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message("حُفظت الملاحظة")
            else:
                from utils import show_info_message
                show_info_message("تم", "حُفظت الملاحظة")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل الحفظ: {str(e)}")
            else:
                from utils import show_error_message
                show_error_message("خطأ", f"فشل الحفظ: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي - موحد مع باقي النوافذ"""
        try:
            # إنشاء أيقونة مخصصة متطورة
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج متطور للملاحظات
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(168, 85, 247))
            gradient.setColorAt(0.7, QColor(124, 58, 237))
            gradient.setColorAt(1, QColor(109, 40, 217))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📝")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))

            # تطبيق تصميم متطور على شريط العنوان - موحد
            from ui.suppliers import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            try:
                from ui.title_bar_styler import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(self)
            except Exception as e2:
                print(f"خطأ في تخصيص شريط العنوان: {e2}")

    def view_selected_document(self, documents_list, project):
        """عرض تفاصيل الوثيقة المحددة"""
        current_item = documents_list.currentItem()
        if not current_item:
            dialog = ProjectWarningDialog(self, "تنبيه", "الرجاء اختيار وثيقة من القائمة", "⚠️")
            dialog.exec_()
            return

        document_name = current_item.text()

        # إنشاء نافذة تفاصيل الوثيقة
        dialog = QDialog(self)
        dialog.setWindowTitle(f"👁️ تفاصيل الوثيقة")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout()

        # تفاصيل الوثيقة
        details_text = f"""
📄 تفاصيل الوثيقة:
─────────────────────────────────────────────────────────────────────────────
🏗️ المشروع: {project.name}
📁 اسم الوثيقة: {document_name}
📅 تاريخ الإضافة: {QDate.currentDate().toString('yyyy-MM-dd')}
👤 أضيفت بواسطة: المستخدم الحالي
📊 حجم الملف: 2.5 ميجابايت (تجريبي)
🔗 النوع: {document_name.split('.')[-1] if '.' in document_name else 'غير محدد'}

📝 الوصف:
هذه وثيقة تجريبية مرتبطة بالمشروع. في النسخة المكتملة ستعرض التفاصيل الحقيقية للوثيقة.

🔧 الإجراءات المتاحة:
• عرض الوثيقة
• تحميل الوثيقة
• تعديل تفاصيل الوثيقة
• حذف الوثيقة
        """

        details_label = QLabel(details_text)
        details_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
        layout.addWidget(details_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        open_btn = QPushButton("📂 فتح الوثيقة")
        open_btn.clicked.connect(lambda: ProjectWarningDialog(self, "قريباً", "ميزة فتح الوثيقة ستكون متاحة قريباً!", "🔧").exec_())
        buttons_layout.addWidget(open_btn)

        download_btn = QPushButton("⬇️ تحميل")
        download_btn.clicked.connect(lambda: ProjectWarningDialog(self, "قريباً", "ميزة تحميل الوثيقة ستكون متاحة قريباً!", "🔧").exec_())
        buttons_layout.addWidget(download_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def delete_selected_document(self, documents_list, project):
        """حذف الوثيقة المحددة"""
        current_item = documents_list.currentItem()
        if not current_item:
            dialog = ProjectWarningDialog(self, "تنبيه", "الرجاء اختيار وثيقة من القائمة", "⚠️")
            dialog.exec_()
            return

        document_name = current_item.text()

        # تأكيد الحذف
        from utils import show_confirmation_message
        if show_confirmation_message(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الوثيقة:\n\n📄 {document_name}\n\nلا يمكن التراجع عن هذا الإجراء!"
        ):
            # حذف العنصر من القائمة
            row = documents_list.row(current_item)
            documents_list.takeItem(row)

            dialog = ProjectSuccessDialog(self, "تم الحذف", f"تم حذف الوثيقة بنجاح:\n\n📄 {document_name}\n\nملاحظة: في النسخة المكتملة ستتم إزالة الوثيقة من قاعدة البيانات أيضاً.", "✅")
            dialog.exec_()

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير"""
        try:
            # إنشاء إطار للقائمة المخصصة
            self.status_filter_frame = QFrame()
            self.status_filter_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 6px 15px;
                    min-width: 500px;
                    max-width: 500px;
                    min-height: 33px;
                    max-height: 37px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
                }
            """)

            # إنشاء تخطيط أفقي للإطار
            filter_layout = QHBoxLayout(self.status_filter_frame)
            filter_layout.setContentsMargins(5, 0, 5, 0)
            filter_layout.setSpacing(8)

            # سهم يسار
            self.left_arrow = QPushButton("▼")
            self.left_arrow.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # النص الحالي
            self.current_filter_label = QLabel("جميع الحالات")
            self.current_filter_label.setAlignment(Qt.AlignCenter)
            self.current_filter_label.setStyleSheet("""
                QLabel {
                    color: #1f2937;
                    font-size: 16px;
                    font-weight: 900;
                    background: transparent;
                    border: none;
                    padding: 0px 12px;
                    text-align: center;
                    max-width: 435px;
                    min-width: 435px;
                    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                    cursor: pointer;
                }
            """)

            # زر القائمة (سهم يمين)
            self.filter_menu_button = QPushButton("▼")
            self.filter_menu_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # إضافة العناصر للتخطيط
            filter_layout.addWidget(self.left_arrow, 0)
            filter_layout.addWidget(self.current_filter_label, 1)
            filter_layout.addWidget(self.filter_menu_button, 0)

            # إنشاء القائمة المنسدلة للحالات
            self.filter_menu = QMenu(self)
            self.filter_menu.setStyleSheet("""
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 4px;
                    padding: 8px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    min-width: 515px;
                    max-width: 515px;
                }
                QMenu::item {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 8px;
                    margin: 2px 5px;
                    min-height: 32px;
                    max-height: 32px;
                    max-width: 495px;
                    min-width: 495px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    text-align: center;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                }
                QMenu::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(96, 165, 250, 0.4),
                        stop:0.2 rgba(139, 92, 246, 0.3),
                        stop:0.4 rgba(124, 58, 237, 0.25),
                        stop:0.6 rgba(139, 92, 246, 0.3),
                        stop:0.8 rgba(96, 165, 250, 0.4),
                        stop:1 rgba(59, 130, 246, 0.35));
                    border: 3px solid rgba(96, 165, 250, 0.7);
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                    box-shadow:
                        0 4px 12px rgba(96, 165, 250, 0.3),
                        0 0 15px rgba(96, 165, 250, 0.2),
                        inset 0 1px 2px rgba(255, 255, 255, 0.5);
                    transform: scale(1.02);
                }
                QMenu::item:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                    transform: translateY(-1px);
                }
            """)

            # إضافة خيارات التصفية للحالات مع أيقونات مطابقة للعملاء
            status_options = [
                ("جميع الحالات", None),
                ("🟡 قيد التخطيط", "planning"),
                ("🔵 قيد التنفيذ", "in_progress"),
                ("🟢 مكتملة", "completed"),
                ("🔴 ملغاة", "cancelled")
            ]

            for text, value in status_options:
                # إنشاء عنصر مع توسيط النص المثالي
                centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
                action = QAction(centered_text, self)
                action.setData(value)
                action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
                self.filter_menu.addAction(action)

            # ربط الأزرار بالقائمة
            self.filter_menu_button.clicked.connect(self.show_filter_menu)
            self.left_arrow.clicked.connect(self.show_filter_menu)

            # إضافة ميزة الضغط على أي مكان في الإطار
            self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
            self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

            # تعيين القيم الافتراضية
            self.current_filter_value = None
            print("✅ تم إنشاء قائمة التصفية المخصصة للمشاريع بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة التصفية المخصصة للمشاريع: {str(e)}")
            import traceback
            traceback.print_exc()
            # إنشاء قائمة بسيطة كبديل
            self.status_filter_frame = QLabel("جميع الحالات")
            self.current_filter_value = None

    def show_filter_menu(self):
        """عرض قائمة تصفية الحالات"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.filter_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.filter_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة الحالات: {str(e)}")
            # عرض القائمة في موقع افتراضي
            self.filter_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))

    def set_filter(self, value, text):
        """تعيين تصفية الحالة"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_projects()

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الحالة"""
        self.show_filter_menu()


class ProjectStatisticsDialog(QDialog):
    """نافذة إحصائيات المشاريع مطابقة للعملاء والموردين والعمال"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي مع شريط عنوان موحد
        self.setWindowTitle("📊 إحصائيات المشاريع - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(600, 420)  # نفس ارتفاع نافذة العملاء

        # تخصيص شريط العنوان الموحد
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات من 20 إلى 12

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 10px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(8)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات المشاريع")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 30px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 10px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة المشاريع والميزانيات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(8)  # تقليل المسافة أكثر من 15 إلى 8
        stats_layout.setContentsMargins(20, 10, 20, 10)  # تقليل الهوامش أكثر

        # إنشاء قائمة الإحصائيات
        stats_items = [
            ("🏗️", "إجمالي المشاريع المسجلة في النظام", str(self.total_projects), "#3B82F6"),
            ("✅", "المشاريع النشطة (قيد التنفيذ)", str(self.active_projects), "#10B981"),
            ("⚪", "المشاريع المكتملة بنجاح", str(self.completed_projects), "#F59E0B"),
            ("❌", "المشاريع المتوقفة أو الملغية", str(self.stopped_projects), "#EF4444"),
            ("💰", "إجمالي الميزانيات المخصصة", format_currency(self.total_budget), "#10B981")
        ]

        for icon, title, value, color in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;  /* تقليل من 8px إلى 5px */
                    margin: 1px;   /* تقليل من 2px إلى 1px */
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(12)  # تقليل من 15 إلى 12
            item_layout.setContentsMargins(12, 5, 12, 5)  # تقليل الهوامش

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(28, 28)  # تصغير أكثر
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;  /* تصغير أكثر */
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 5px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    padding: 4px 8px;
                }
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 6px 12px;
                    min-width: 70px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تصدير PDF
        export_pdf_button = QPushButton("📄 تصدير PDF")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        export_pdf_button.setMinimumHeight(45)
        self.style_advanced_button(export_pdf_button, 'info')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب إحصائيات المشاريع"""
        try:
            # حساب إجمالي المشاريع
            self.total_projects = self.session.query(Project).count()

            # حساب المشاريع حسب الحالة (افتراض أن هناك حقل status)
            self.active_projects = self.session.query(Project).filter(Project.status == 'نشط').count()
            self.completed_projects = self.session.query(Project).filter(Project.status == 'مكتمل').count()
            self.stopped_projects = self.session.query(Project).filter(Project.status.in_(['متوقف', 'ملغي'])).count()

            # حساب إجمالي الميزانيات
            total_budget_result = self.session.query(func.sum(Project.budget)).scalar()
            self.total_budget = total_budget_result or 0

        except Exception as e:
            print(f"خطأ في حساب إحصائيات المشاريع: {e}")
            self.total_projects = 0
            self.active_projects = 0
            self.completed_projects = 0
            self.stopped_projects = 0
            self.total_budget = 0

    def export_statistics_to_pdf(self):
        """تصدير الإحصائيات إلى PDF - دالة مؤقتة"""
        dialog = ProjectWarningDialog(self, "تطوير", "ميزة تصدير PDF قيد التطوير", "🔧")
        dialog.exec_()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")


class ProjectDocumentsDialog(QDialog):
    """نافذة إدارة وثائق المشروع - مطابقة للعملاء والموردين والعمال"""

    def __init__(self, parent=None, project=None, session=None):
        super().__init__(parent)
        self.project = project
        self.session = session
        self.documents = []
        self.setup_ui()
        self.load_documents()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle(f"📁 إدارة وثائق المشروع - {self.project.name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(800, 600)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للعملاء
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط النافذة (مضغوط جداً لتوفير المساحة)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # تقليل الهوامش أكثر
        layout.setSpacing(8)  # تقليل المسافات أكثر

        # العنوان الرئيسي المطور بدون إطار (مثل نافذة الإحصائيات)
        title_container = QWidget()
        title_container.setStyleSheet("background: transparent;")
        title_inner_layout = QVBoxLayout()
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(2)
        title_container.setLayout(title_inner_layout)

        # العنوان الرئيسي
        main_title = QLabel(f"📁 إدارة وثائق المشروع")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                padding: 8px;
            }
        """)

        # العنوان الفرعي
        subtitle = QLabel(f"المشروع: {self.project.name}")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 4px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # قائمة الوثائق
        self.documents_list = QListWidget()
        self.documents_list.setMinimumHeight(150)  # تقليل الحد الأدنى للارتفاع أكثر
        self.documents_list.setStyleSheet("""
            QListWidget {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 8px;
                font-size: 13px;
                color: #ffffff;
            }
            QListWidget::item {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
                margin: 2px;
                font-weight: bold;
            }
            QListWidget::item:selected {
                background: rgba(34, 197, 94, 0.3);
                border: 2px solid rgba(34, 197, 94, 0.6);
                color: #ffffff;
            }
            QListWidget::item:hover {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.4);
            }
        """)

        # ربط النقر المزدوج بعرض الوثيقة
        self.documents_list.itemDoubleClicked.connect(self.view_document)

        layout.addWidget(self.documents_list, 1)  # إعطاء أولوية التمدد للقائمة

        # إطار واحد لجميع الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 8px;
                margin: 2px;
            }
        """)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # الصف الأول - أزرار الإضافة
        add_buttons_layout = QHBoxLayout()
        add_buttons_layout.setSpacing(10)

        # زر إضافة بطاقة هوية
        id_card_button = QPushButton("🆔 إضافة بطاقة هوية")
        id_card_button.clicked.connect(lambda: self.add_document("بطاقة هوية"))
        id_card_button.setMinimumHeight(45)
        self.style_advanced_button(id_card_button, 'emerald')

        # زر إضافة عقد
        contract_button = QPushButton("📋 إضافة عقد")
        contract_button.clicked.connect(lambda: self.add_document("عقد"))
        contract_button.setMinimumHeight(45)
        self.style_advanced_button(contract_button, 'info')

        # زر إضافة وثيقة أخرى
        other_doc_button = QPushButton("📄 إضافة وثيقة")
        other_doc_button.clicked.connect(lambda: self.add_document("وثيقة"))
        other_doc_button.setMinimumHeight(45)
        self.style_advanced_button(other_doc_button, 'emerald')

        add_buttons_layout.addWidget(id_card_button)
        add_buttons_layout.addWidget(contract_button)
        add_buttons_layout.addWidget(other_doc_button)

        # الصف الثاني - أزرار الإجراءات
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setSpacing(10)

        view_button = QPushButton("👁️ عرض الوثيقة")
        view_button.clicked.connect(self.view_document)
        view_button.setMinimumHeight(45)
        self.style_advanced_button(view_button, 'info')

        delete_button = QPushButton("🗑️ حذف الوثيقة")
        delete_button.clicked.connect(self.delete_document)
        delete_button.setMinimumHeight(45)
        self.style_advanced_button(delete_button, 'danger')

        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'secondary')

        action_buttons_layout.addWidget(view_button)
        action_buttons_layout.addWidget(delete_button)
        action_buttons_layout.addWidget(close_button)

        # إضافة الصفوف للإطار
        buttons_layout.addLayout(add_buttons_layout)
        buttons_layout.addLayout(action_buttons_layout)

        layout.addWidget(buttons_frame)

    def load_documents(self):
        """تحميل قائمة الوثائق من قاعدة البيانات"""
        try:
            self.documents_list.clear()
            self.documents = []

            # تحميل الوثائق من قاعدة البيانات
            db_documents = self.session.query(Document).filter(
                Document.project_id == self.project.id
            ).all()

            for doc in db_documents:
                # إضافة للقائمة المحلية
                document = {
                    'id': doc.id,
                    'title': doc.title,
                    'description': doc.description,
                    'file_path': doc.file_path,
                    'type': doc.file_type,
                    'project_id': doc.project_id,
                    'created_at': doc.upload_date
                }
                self.documents.append(document)

                # إضافة للقائمة المرئية
                item_text = f"📄 {doc.title}"
                if doc.description:
                    item_text += f"\n   📝 {doc.description}"
                if doc.upload_date:
                    item_text += f"\n   📅 {doc.upload_date.strftime('%Y-%m-%d')}"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, doc.id)
                self.documents_list.addItem(item)

        except Exception as e:
            print(f"خطأ في تحميل الوثائق: {e}")
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في تحميل الوثائق: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في تحميل الوثائق: {str(e)}")

    def add_document(self, doc_type):
        """إضافة وثيقة جديدة مباشرة"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self, f"اختر {doc_type}", "",
                "جميع الملفات (*.*);;ملفات PDF (*.pdf);;ملفات الصور (*.jpg *.jpeg *.png);;ملفات Word (*.doc *.docx)"
            )

            if file_path:
                # إنشاء عنوان تلقائي من اسم الملف
                import os
                file_name = os.path.basename(file_path)
                title = f"{doc_type} - {file_name}"
                description = f"وثيقة {doc_type} للمشروع {self.project.name}"

                # حفظ الوثيقة في قاعدة البيانات
                try:
                    # إنشاء وثيقة جديدة
                    new_document = Document(
                        title=title,
                        description=description,
                        file_path=file_path,
                        file_type=doc_type,
                        project_id=self.project.id
                    )

                    # حفظ في قاعدة البيانات
                    self.session.add(new_document)
                    self.session.commit()

                    if hasattr(self.parent_widget, 'show_success_message'):
                        self.parent_widget.show_success_message(f"تم حفظ {doc_type} بنجاح")
                    else:
                        show_info_message("تم الحفظ", f"تم حفظ {doc_type} بنجاح")
                    self.load_documents()  # إعادة تحميل القائمة

                except Exception as db_error:
                    self.session.rollback()
                    if hasattr(self.parent_widget, 'show_error_message'):
                        self.parent_widget.show_error_message(f"حدث خطأ في حفظ الوثيقة:\n{str(db_error)}")
                    else:
                        show_error_message("خطأ في الحفظ", f"حدث خطأ في حفظ الوثيقة:\n{str(db_error)}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إضافة الوثيقة: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إضافة الوثيقة: {str(e)}")

    def view_document(self):
        """عرض الوثيقة المحددة"""
        try:
            current_item = self.documents_list.currentItem()
            if not current_item:
                if hasattr(self.parent_widget, 'show_error_message'):
                    self.parent_widget.show_error_message("الرجاء اختيار وثيقة من القائمة")
                else:
                    show_error_message("خطأ", "الرجاء اختيار وثيقة من القائمة")
                return

            document_id = current_item.data(Qt.UserRole)
            document = next((doc for doc in self.documents if doc['id'] == document_id), None)

            if document and document['file_path']:
                import os
                import subprocess
                import platform

                file_path = document['file_path']
                if os.path.exists(file_path):
                    try:
                        if platform.system() == 'Windows':
                            os.startfile(file_path)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.run(['open', file_path])
                        else:  # Linux
                            subprocess.run(['xdg-open', file_path])
                    except Exception as e:
                        if hasattr(self.parent_widget, 'show_error_message'):
                            self.parent_widget.show_error_message(f"لا يمكن فتح الملف:\n{str(e)}")
                        else:
                            show_error_message("خطأ", f"لا يمكن فتح الملف:\n{str(e)}")
                else:
                    if hasattr(self.parent_widget, 'show_error_message'):
                        self.parent_widget.show_error_message("الملف غير موجود في المسار المحدد")
                    else:
                        show_error_message("خطأ", "الملف غير موجود في المسار المحدد")
            else:
                if hasattr(self.parent_widget, 'show_error_message'):
                    self.parent_widget.show_error_message("لم يتم العثور على الوثيقة")
                else:
                    show_error_message("خطأ", "لم يتم العثور على الوثيقة")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في عرض الوثيقة: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في عرض الوثيقة: {str(e)}")

    def delete_document(self):
        """حذف الوثيقة المحددة"""
        try:
            current_item = self.documents_list.currentItem()
            if not current_item:
                if hasattr(self.parent_widget, 'show_error_message'):
                    self.parent_widget.show_error_message("الرجاء اختيار وثيقة من القائمة")
                else:
                    show_error_message("خطأ", "الرجاء اختيار وثيقة من القائمة")
                return

            document_id = current_item.data(Qt.UserRole)
            document = next((doc for doc in self.documents if doc['id'] == document_id), None)

            if document:
                # تأكيد الحذف
                if hasattr(self.parent_widget, 'show_confirmation_message'):
                    reply = self.parent_widget.show_confirmation_message(
                        "تأكيد الحذف",
                        f"هل أنت متأكد من حذف الوثيقة:\n\n📄 {document['title']}\n\nلا يمكن التراجع عن هذا الإجراء!"
                    )
                else:
                    reply = show_confirmation_message(
                        "تأكيد الحذف",
                        f"هل أنت متأكد من حذف الوثيقة:\n\n📄 {document['title']}\n\nلا يمكن التراجع عن هذا الإجراء!"
                    )

                if reply:
                    try:
                        # حذف من قاعدة البيانات
                        db_document = self.session.query(Document).filter(
                            Document.id == document_id
                        ).first()

                        if db_document:
                            self.session.delete(db_document)
                            self.session.commit()

                            if hasattr(self.parent_widget, 'show_success_message'):
                                self.parent_widget.show_success_message("تم حذف الوثيقة بنجاح")
                            else:
                                show_info_message("تم الحذف", "تم حذف الوثيقة بنجاح")
                            self.load_documents()  # إعادة تحميل القائمة
                        else:
                            if hasattr(self.parent_widget, 'show_error_message'):
                                self.parent_widget.show_error_message("لم يتم العثور على الوثيقة في قاعدة البيانات")
                            else:
                                show_error_message("خطأ", "لم يتم العثور على الوثيقة في قاعدة البيانات")

                    except Exception as db_error:
                        self.session.rollback()
                        if hasattr(self.parent_widget, 'show_error_message'):
                            self.parent_widget.show_error_message(f"حدث خطأ في حذف الوثيقة:\n{str(db_error)}")
                        else:
                            show_error_message("خطأ في الحذف", f"حدث خطأ في حذف الوثيقة:\n{str(db_error)}")
            else:
                if hasattr(self.parent_widget, 'show_error_message'):
                    self.parent_widget.show_error_message("لم يتم العثور على الوثيقة")
                else:
                    show_error_message("خطأ", "لم يتم العثور على الوثيقة")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في حذف الوثيقة: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في حذف الوثيقة: {str(e)}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار مطابق للنوافذ الأخرى"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent() and hasattr(self.parent(), 'style_advanced_button'):
                self.parent().style_advanced_button(button, button_type)
            else:
                # تصميم متطور مطابق للنوافذ الأخرى
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6',
                    'warning': '#f59e0b',
                    'secondary': '#6b7280',
                    'cyan': '#06b6d4'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:0.5 {color}dd, stop:1 {color}bb);
                        color: white;
                        border: 2px solid {color};
                        border-radius: 10px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}ee, stop:0.5 {color}cc, stop:1 {color}aa);
                        border: 3px solid {color};
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}aa, stop:0.5 {color}88, stop:1 {color}66);
                        transform: translateY(1px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")


